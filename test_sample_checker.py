#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
IP端口连通性测试脚本 - 样本测试版本
选取5个TCP和5个UDP协议进行验证测试
"""

import pandas as pd
import socket
import threading
import time
from datetime import datetime
import sys
import os
import argparse
import signal
from concurrent.futures import ThreadPoolExecutor, as_completed

class PortChecker:
    def __init__(self, timeout=3):
        """
        初始化端口检查器
        
        Args:
            timeout (int): 连接超时时间（秒）
        """
        self.timeout = timeout
        
    def check_tcp_port(self, ip, port):
        """
        检查TCP端口连通性
        
        Args:
            ip (str): IP地址
            port (int): 端口号
            
        Returns:
            bool: True表示连通，False表示不连通
        """
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except Exception as e:
            print(f"TCP检查异常 {ip}:{port} - {str(e)}")
            return False
    
    def check_udp_port(self, ip, port):
        """
        检查UDP端口连通性（宽松模式）
        只检查是否能发送数据包

        Args:
            ip (str): IP地址
            port (int): 端口号

        Returns:
            bool: True表示可能连通，False表示不连通
        """
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(self.timeout)
            sock.sendto(b'', (ip, port))
            sock.close()
            print(f"UDP宽松测试: {ip}:{port} - 可以发送数据包")
            return True
        except Exception as e:
            print(f"UDP检查异常 {ip}:{port} - {str(e)}")
            return False

    def check_udp_port_accurate(self, ip, port):
        """
        检查UDP端口连通性（严格模式）
        发送探测包并等待响应，只有收到响应才认为端口连通

        Args:
            ip (str): IP地址
            port (int): 端口号

        Returns:
            bool: True表示连通，False表示不连通
        """
        try:
            # 创建UDP套接字
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(self.timeout)

            # 发送一个探测包并等待响应
            # 对于一些常见的UDP服务，发送特定的探测包可能会得到响应
            if port == 53:  # DNS
                sock.sendto(b'\x00\x00\x01\x00\x00\x01\x00\x00\x00\x00\x00\x00\x03www\x06google\x03com\x00\x00\x01\x00\x01', (ip, port))
            elif port == 161:  # SNMP
                # SNMP GET请求
                sock.sendto(b'\x30\x26\x02\x01\x01\x04\x06\x70\x75\x62\x6c\x69\x63\xa0\x19\x02\x01\x01\x02\x01\x00\x02\x01\x00\x30\x0e\x30\x0c\x06\x08\x2b\x06\x01\x02\x01\x01\x01\x00\x05\x00', (ip, port))
            else:
                # 对于其他端口，发送一个通用探测包
                sock.sendto(b'UDP Port Probe\r\n', (ip, port))

            # 尝试接收响应
            try:
                data, addr = sock.recvfrom(1024)
                print(f"  📨 UDP端口 {ip}:{port} 收到响应: {len(data)} 字节")
                sock.close()
                return True
            except socket.timeout:
                # 超时没有收到响应
                print(f"  ⏰ UDP端口 {ip}:{port} 超时无响应")
                sock.close()
                return False
        except Exception as e:
            print(f"UDP检查异常 {ip}:{port} - {str(e)}")
            return False
    
    def check_single_port(self, row_data):
        """
        检查单个IP端口的连通性
        
        Args:
            row_data (tuple): (index, ip, port, protocol, 网元编码)
            
        Returns:
            tuple: (index, status, test_time, details)
        """
        index, ip, port, protocol, 网元编码 = row_data
        test_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        try:
            port = int(port)
            protocol = str(protocol).upper().strip()
            
            protocol_emoji = "🔗" if protocol == 'TCP' else "📡"
            print(f"🔍 开始测试: {protocol_emoji} {ip}:{port} ({protocol}) - {网元编码}")
            
            if protocol == 'TCP':
                is_connected = self.check_tcp_port(ip, port)
            elif protocol == 'UDP':
                # 使用严格模式的UDP测试
                is_connected = self.check_udp_port_accurate(ip, port)
            else:
                print(f"未知协议: {protocol}, 默认使用TCP")
                is_connected = self.check_tcp_port(ip, port)
            
            status = "连通" if is_connected else "不连通"
            status_emoji = "✅" if is_connected else "❌"
            details = f"{ip}:{port} ({protocol}) - {status}"
            print(f"  {status_emoji} 测试完成: {details}")
            
        except Exception as e:
            status = f"检查失败: {str(e)}"
            details = f"{ip}:{port} - {str(e)}"
            print(f"  ⚠️ 测试异常: {details}")
        
        return index, status, test_time, details

def load_and_sample_data(file_path, tcp_count=5, udp_count=5, filter_conditions=None):
    """
    加载Excel文件并选取样本数据

    Args:
        file_path (str): Excel文件路径
        tcp_count (int): TCP样本数量
        udp_count (int): UDP样本数量
        filter_conditions (dict): 筛选条件字典

    Returns:
        pandas.DataFrame: 样本数据框
    """
    try:
        df = pd.read_excel(file_path)
        print(f"📂 成功加载文件: {file_path}")
        print(f"📊 原始数据形状: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        
        # 应用筛选条件
        original_count = len(df)
        if filter_conditions:
            print(f"\n🔍 应用筛选条件:")
            for column, values in filter_conditions.items():
                if column in df.columns:
                    if isinstance(values, list):
                        df = df[df[column].isin(values)]
                        print(f"  📋 {column} 包含: {', '.join(map(str, values))}")
                    else:
                        df = df[df[column] == values]
                        print(f"  📋 {column} 等于: {values}")
                else:
                    print(f"  ⚠️ 警告: 列 '{column}' 不存在，跳过此筛选条件")

            filtered_count = len(df)
            print(f"  📊 筛选结果: {original_count} → {filtered_count} 条记录")

            if filtered_count == 0:
                print("  ❌ 筛选后没有数据，请检查筛选条件")
                return None

        # 统计协议分布
        protocol_counts = df['协议'].value_counts()
        print(f"\n📈 协议统计:")
        for protocol, count in protocol_counts.items():
            emoji = "🔗" if protocol == 'TCP' else "📡"
            print(f"  {emoji} {protocol}: {count} 个")
        
        # 选取TCP样本
        tcp_data = df[df['协议'] == 'TCP'].head(tcp_count)
        print(f"\n🔗 选取的TCP样本 ({len(tcp_data)}个):")
        for i, row in tcp_data.iterrows():
            print(f"  📍 {row['IP']}:{row['端口']} - {row['网元编码']} ({row.get('网元类型', 'N/A')})")

        # 选取UDP样本
        udp_data = df[df['协议'] == 'UDP'].head(udp_count)
        print(f"\n📡 选取的UDP样本 ({len(udp_data)}个):")
        for i, row in udp_data.iterrows():
            print(f"  📍 {row['IP']}:{row['端口']} - {row['网元编码']} ({row.get('网元类型', 'N/A')})")

        # 合并样本数据
        sample_data = pd.concat([tcp_data, udp_data], ignore_index=True)
        print(f"\n📦 样本数据总计: {len(sample_data)} 个")
        
        return sample_data
        
    except Exception as e:
        print(f"❌ 加载文件失败: {str(e)}")
        return None

def parse_arguments():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(
        description='🔍 IP端口连通性测试工具 - 样本测试版本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
📋 使用示例:
  python test_sample_checker.py                                    # 使用默认筛选条件（网络类型=5GC,DC），按属地分组输出
  python test_sample_checker.py --no-filter                        # 不使用筛选条件，按属地分组输出
  python test_sample_checker.py --filter "网元类型=IBCF,DCGW"      # 自定义筛选条件
  python test_sample_checker.py --tcp-count 10 --udp-count 10      # 设置样本数量
  python test_sample_checker.py --timeout 10                       # 设置超时时间
  python test_sample_checker.py --no-group-by-region               # 不按属地分组，保存到单个文件
  python test_sample_checker.py --region HBE                       # 只测试湖北属地
  python test_sample_checker.py --list-only                        # 只显示测试列表，不执行测试
        """
    )

    parser.add_argument('--input-file', '-i',
                       default='全国ip端口.xlsx',
                       help='📂 输入Excel文件路径 (默认: 全国ip端口.xlsx)')

    parser.add_argument('--tcp-count', '-t',
                       type=int, default=5,
                       help='🔗 TCP样本数量 (默认: 5)')

    parser.add_argument('--udp-count', '-u',
                       type=int, default=5,
                       help='📡 UDP样本数量 (默认: 5)')

    parser.add_argument('--timeout',
                       type=int, default=5,
                       help='⏱️ 连接超时时间（秒） (默认: 5)')

    parser.add_argument('--filter', '-f',
                       help='🔍 筛选条件，格式: "列名=值1,值2" 或 "列名1=值1;列名2=值2" (默认: 网络类型=5GC,DC)')

    parser.add_argument('--no-filter',
                       action='store_true',
                       help='🚫 不使用任何筛选条件')

    parser.add_argument('--list-columns',
                       action='store_true',
                       help='📋 显示可用的列名并退出')

    parser.add_argument('--no-group-by-region',
                       action='store_true',
                       help='🚫 不按属地分组输出，所有结果保存在一个文件中')

    parser.add_argument('--region', '-r',
                       help='🗺️ 指定测试某个属地（如：HBE, YND, HNY等）')

    parser.add_argument('--list-only',
                       action='store_true',
                       help='📋 只输出要测试的列表，不执行实际测试')

    return parser.parse_args()

def parse_filter_conditions(filter_str):
    """
    解析筛选条件字符串

    Args:
        filter_str (str): 筛选条件字符串，格式: "列名=值1,值2" 或 "列名1=值1;列名2=值2"

    Returns:
        dict: 筛选条件字典
    """
    if not filter_str:
        return None

    conditions = {}
    # 按分号分割不同的筛选条件
    for condition in filter_str.split(';'):
        if '=' in condition:
            column, values = condition.split('=', 1)
            column = column.strip()
            # 按逗号分割多个值
            value_list = [v.strip() for v in values.split(',')]
            if len(value_list) == 1:
                conditions[column] = value_list[0]
            else:
                conditions[column] = value_list

    return conditions

def signal_handler(signum, frame):
    """
    信号处理函数，用于优雅地处理Ctrl+C中断 (Linux环境)
    """
    print(f"\n\n🛑 收到中断信号 (Ctrl+C)，正在停止测试...")
    print("⏳ 请稍等，正在清理资源...")
    print("💡 提示：在Linux环境下，中断处理更加可靠")
    sys.exit(0)

def main():
    """
    主函数
    """
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)

    # 解析命令行参数
    args = parse_arguments()

    # 配置参数
    input_file = args.input_file
    tcp_count = args.tcp_count
    udp_count = args.udp_count
    timeout = args.timeout

    # 创建输出目录
    output_dir = "check-result"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    # 生成带时间戳的输出文件名
    current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
    group_by_region = not args.no_group_by_region

    # UDP测试模式: 'strict'=严格模式(默认，更准确但可能误报不通)，'loose'=宽松模式(可能误报连通)
    udp_test_mode = 'strict'

    # 处理筛选条件
    if args.no_filter:
        filter_conditions = None
        print("🚫 不使用筛选条件")
    elif args.filter:
        filter_conditions = parse_filter_conditions(args.filter)
        print(f"🔍 使用自定义筛选条件: {args.filter}")
    else:
        # 默认筛选条件：网络类型是5GC和DC
        filter_conditions = {'网络类型': ['5GC', 'DC']}
        print("🎯 使用默认筛选条件: 网络类型=5GC,DC")

    # 处理属地筛选
    if args.region:
        if filter_conditions is None:
            filter_conditions = {}
        filter_conditions['属地'] = args.region
        print(f"🗺️ 指定测试属地: {args.region}")
    
    print("🌐" + "=" * 58 + "🌐")
    print("🔍 IP端口连通性测试工具 - 样本测试版本 🔍")
    print("🌐" + "=" * 58 + "🌐")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误: 输入文件 '{input_file}' 不存在")
        return

    # 如果只是要查看列名，则加载文件并显示列名后退出
    if args.list_columns:
        try:
            df = pd.read_excel(input_file)
            print("📋 可用的列名:")
            for i, col in enumerate(df.columns, 1):
                print(f"  {i:2d}. {col}")
            print(f"\n📊 数据形状: {df.shape}")
            return
        except Exception as e:
            print(f"❌ 加载文件失败: {str(e)}")
            return

    # 加载并选取样本数据
    df = load_and_sample_data(input_file, tcp_count, udp_count, filter_conditions)
    if df is None or len(df) == 0:
        print("❌ 没有可测试的数据")
        return

    # 如果只是显示列表，则输出测试列表后退出
    if args.list_only:
        # 确定要显示的列
        display_columns = ['IP', '端口', '协议']

        # 根据筛选条件添加相关列
        if filter_conditions:
            for column in filter_conditions.keys():
                if column in df.columns and column not in display_columns:
                    display_columns.append(column)

        # 如果指定了属地且属地列不在显示列中，添加属地列
        if args.region and '属地' in df.columns and '属地' not in display_columns:
            display_columns.append('属地')

        print(f"\n📋 要测试的IP端口列表:")

        # 动态计算列宽
        col_widths = {}
        col_widths['序号'] = 4
        col_widths['IP'] = 15
        col_widths['端口'] = 6
        col_widths['协议'] = 6

        for col in display_columns[3:]:  # 跳过IP、端口、协议
            if col in df.columns:
                max_len = max(len(str(col)), df[col].astype(str).str.len().max())
                col_widths[col] = min(max_len + 2, 20)  # 最大宽度20

        # 构建表头
        header_line = f"{'序号':<{col_widths['序号']}} {'IP地址':<{col_widths['IP']}} {'端口':<{col_widths['端口']}} {'协议':<{col_widths['协议']}}"
        separator_line = "-" * (col_widths['序号'] + col_widths['IP'] + col_widths['端口'] + col_widths['协议'])

        for col in display_columns[3:]:
            if col in df.columns:
                header_line += f" {col:<{col_widths[col]}}"
                separator_line += "-" * (col_widths[col] + 1)

        print("=" * len(separator_line))
        print(header_line)
        print(separator_line)

        # 输出数据行
        for i, (index, row) in enumerate(df.iterrows(), 1):
            data_line = f"{i:<{col_widths['序号']}} {row['IP']:<{col_widths['IP']}} {row['端口']:<{col_widths['端口']}} {row['协议']:<{col_widths['协议']}}"

            for col in display_columns[3:]:
                if col in df.columns:
                    value = str(row[col])[:col_widths[col]-2]  # 截断过长的值
                    data_line += f" {value:<{col_widths[col]}}"

            print(data_line)

        print("=" * len(separator_line))
        print(f"📊 总计: {len(df)} 个IP端口待测试")

        # 按协议统计
        protocol_counts = df['协议'].value_counts()
        print(f"\n📈 协议统计:")
        for protocol, count in protocol_counts.items():
            emoji = "🔗" if protocol == 'TCP' else "📡"
            print(f"  {emoji} {protocol}: {count} 个")

        # 显示筛选条件相关的统计
        if filter_conditions:
            for column in filter_conditions.keys():
                if column in df.columns and column != '协议':
                    counts = df[column].value_counts()
                    print(f"\n📊 {column}统计:")
                    for value, count in counts.items():
                        print(f"  📍 {value}: {count} 个")

        print(f"\n💡 要执行实际测试，请去掉 --list-only 参数")
        return
    
    # 初始化端口检查器
    checker = PortChecker(timeout=timeout)

    # 显示UDP测试模式
    print(f"🔧 UDP测试模式: {udp_test_mode} ({'严格模式' if udp_test_mode == 'strict' else '宽松模式'})")
    if udp_test_mode == 'strict':
        print("  🎯 严格模式: 只有收到响应才认为UDP端口连通，更准确但可能误报不通")
    else:
        print("  ⚡ 宽松模式: 只要能发送数据包就认为UDP端口可能连通，可能误报连通")
    
    # 准备测试数据
    test_data = []
    for index, row in df.iterrows():
        test_data.append((index, row['IP'], row['端口'], row['协议'], row['网元编码']))
    
    print(f"\n🚀 开始测试 {len(test_data)} 个IP端口...")
    print(f"⏱️  超时时间: {timeout}秒")
    print("🔄" + "-" * 58 + "🔄")
    
    # 初始化结果列
    df['状态'] = ""
    df['测试开始时间'] = ""
    df['测试详情'] = ""
    
    # 逐个进行测试（不使用并发，便于观察结果）
    start_time = time.time()

    try:
        for i, data in enumerate(test_data, 1):
            print(f"\n🔄 [{i}/{len(test_data)}]", end=" ")
            index, status, test_time, details = checker.check_single_port(data)

            # 更新结果
            df.loc[index, '状态'] = status
            df.loc[index, '测试开始时间'] = test_time
            df.loc[index, '测试详情'] = details

            # 短暂延迟，避免过快的连接
            time.sleep(0.5)
    except KeyboardInterrupt:
        print(f"\n\n🛑 测试被用户中断！")
        print(f"📊 已完成 {i-1}/{len(test_data)} 个测试")
        # 继续保存已完成的结果
    
    # 计算总耗时
    total_time = time.time() - start_time
    print(f"\n🎉" + "=" * 58 + "🎉")
    print(f"✅ 测试完成! 总耗时: {total_time:.1f}秒")

    # 统计结果
    status_counts = df['状态'].value_counts()
    print("\n📊 测试结果统计:")
    for status, count in status_counts.items():
        emoji = "❌" if "不连通" in status else "✅" if "连通" in status else "⚠️"
        print(f"  {emoji} {status}: {count} 个")
    
    # 显示详细结果
    print(f"\n📋 详细测试结果:")
    print("📝" + "-" * 78 + "📝")
    for index, row in df.iterrows():
        protocol_emoji = "🔗" if row['协议'] == 'TCP' else "📡"
        status_emoji = "❌" if "不连通" in row['状态'] else "✅" if "连通" in row['状态'] else "⚠️"
        print(f"{protocol_emoji} {row['协议']:3} | {row['IP']:15} | {row['端口']:5} | {status_emoji} {row['状态']:8} | {row['网元编码']}")
    
    # 保存结果
    try:
        if group_by_region and '属地' in df.columns:
            # 按属地分组保存
            print(f"\n📁 按属地分组保存结果...")
            regions = df['属地'].unique()
            saved_files = []

            # 创建按属地分组的统一文件夹
            group_dir = os.path.join(output_dir, f"group_by_region_{current_time}")
            if not os.path.exists(group_dir):
                os.makedirs(group_dir)
                print(f"  📁 创建分组目录: group_by_region_{current_time}")

            for region in regions:
                region_df = df[df['属地'] == region]
                region_file = os.path.join(group_dir, f"样本测试结果_{region}_{current_time}.xlsx")
                region_df.to_excel(region_file, index=False)
                saved_files.append(region_file)
                print(f"  📍 {region}: {len(region_df)} 条记录 → 样本测试结果_{region}_{current_time}.xlsx")

            print(f"\n💾 共保存 {len(saved_files)} 个属地文件到: {group_dir}")
            for file in saved_files:
                print(f"  📄 {os.path.basename(file)}")
        else:
            # 保存到单个文件
            output_file = os.path.join(output_dir, f"样本测试结果_{current_time}.xlsx")
            df.to_excel(output_file, index=False)
            print(f"\n💾 结果已保存到: {output_file}")

    except Exception as e:
        print(f"❌ 保存文件失败: {str(e)}")

if __name__ == "__main__":
    main()
