# 🎯 协议筛选功能说明

## 📋 功能概述

新增了 `--protocol-only` (简写 `-p`) 参数，支持仅测试指定协议（TCP 或 UDP）的端口连通性，筛选出数据中对应协议的记录进行测试。

## 🔧 使用方法

### 基本语法
```bash
python ip_port_checker.py --protocol-only <协议>
python ip_port_checker.py -p <协议>
```

### 支持的协议
- `tcp` 或 `TCP` - 仅测试TCP协议
- `udp` 或 `UDP` - 仅测试UDP协议

## 📝 使用示例

### 1. 仅测试TCP协议
```bash
# 筛选并测试数据中协议为TCP的记录
python ip_port_checker.py --protocol-only tcp

# 简写形式
python ip_port_checker.py -p tcp
```

### 2. 仅测试UDP协议
```bash
# 筛选并测试数据中协议为UDP的记录
python ip_port_checker.py --protocol-only udp

# 简写形式
python ip_port_checker.py -p udp
```

### 3. 结合其他筛选条件
```bash
# 仅测试TCP协议记录，并筛选5GC网络类型
python ip_port_checker.py -p tcp --filter "网络类型=5GC"

# 仅测试UDP协议记录，使用正则表达式筛选IP
python ip_port_checker.py -p udp --regex-filter "IP=^192\.168\..*"

# 测试文本文件，仅筛选TCP协议记录
python ip_port_checker.py -i test_protocol_only.txt -t text -p tcp
```

### 4. 查看测试列表
```bash
# 查看将要测试的列表（仅TCP协议）
python ip_port_checker.py -p tcp --list-only

# 限制显示前10条记录
python ip_port_checker.py -p udp --list-only --limit 10
```

## 🎯 功能特点

### 1. 协议筛选
- 当使用 `--protocol-only` 参数时，程序会筛选出数据中指定协议的记录
- 只测试符合指定协议的IP端口组合
- 等同于使用 `--filter "协议=TCP"` 或 `--filter "协议=UDP"`

### 2. 数据保持
- 筛选后的数据保持原有的协议信息
- 测试时使用数据中原本的协议进行连通性检查
- 结果文件中的协议列显示原始协议信息

### 3. 统计信息
- 协议统计只显示筛选后剩余的协议类型
- 清晰显示实际要测试的协议分布

### 4. 兼容性
- 与现有的所有筛选功能完全兼容
- 支持Excel文件和文本文件两种输入格式
- 支持按属地分组输出等所有现有功能

## 📊 输出示例

### 控制台输出
```
🔗 指定测试协议: TCP
🎯 协议筛选模式: 仅测试 TCP 协议的记录
  📋 将筛选出数据中协议为 TCP 的记录进行测试

📈 协议统计:
  🔗 TCP: 5 个

🚀 开始测试 5 个IP端口...
⏱️  超时时间: 8秒, 🔄 并发数: 50
```

### Excel结果文件
结果文件会包含以下列：
- `IP` - IP地址
- `端口` - 端口号
- `协议` - 数据中的协议（筛选后只有指定协议）
- `状态` - 连通性状态
- `测试开始时间` - 测试时间
- `路由信息` - 路由详情
- `SNMP输出结果` - UDP测试的SNMP结果

## 💡 使用场景

1. **网络故障排查** - 怀疑某个协议有问题时，可以专门测试该协议的记录
2. **协议分析** - 分别测试TCP和UDP协议的连通性情况
3. **数据筛选** - 从混合协议数据中筛选出特定协议进行测试
4. **合规性检查** - 验证特定协议的连通性状况

## ⚠️ 注意事项

1. **UDP测试依赖** - UDP协议测试需要系统安装SNMP工具包
2. **数据完整性** - 筛选后只包含指定协议的记录，其他协议记录会被过滤掉
3. **协议匹配** - 确保数据中的协议标识正确（TCP/UDP，大小写不敏感）
4. **性能影响** - UDP测试通常比TCP测试耗时更长

## 🔄 与现有功能的关系

- `--protocol-only` 参数作为筛选条件，等同于 `--filter "协议=指定协议"`
- 可以与 `--filter`、`--regex-filter` 等其他筛选参数组合使用
- 支持 `--list-only` 预览功能
- 兼容 `--timeout`、`--workers` 等性能参数
- 支持按属地分组输出（Excel文件）
