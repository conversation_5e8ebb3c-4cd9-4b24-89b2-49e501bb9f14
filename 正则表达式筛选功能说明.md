# 🔍 IP端口检查工具 - 正则表达式筛选功能说明

## 📋 功能概述

IP端口检查工具现已支持强大的正则表达式筛选功能，让您可以更灵活地筛选要测试的IP端口数据。

## 🎯 新增功能特性

### 1. 新增参数
- `--regex-filter` 或 `-rf`：专门用于正则表达式筛选
- 与现有的 `--filter/-f` 参数完全兼容，可以同时使用

### 2. 支持的筛选方式
- **普通筛选**：精确匹配或包含匹配
- **正则表达式筛选**：使用正则表达式进行模式匹配
- **组合筛选**：同时使用普通筛选和正则筛选

## 📝 语法格式

### 普通筛选（支持AND/OR逻辑）
```bash
--filter "列名=值1,值2"                    # 多值匹配
--filter "列名1=值1;列名2=值2"             # AND: 多列筛选
--filter "列名1=值1||列名2=值2"            # OR: 条件选择
```

### 正则表达式筛选（支持AND/OR逻辑）
```bash
--regex-filter "列名=正则表达式"           # 单个正则条件
--regex-filter "列名1=正则1;列名2=正则2"   # AND: 多个正则条件
--regex-filter "列名1=正则1||列名2=正则2"  # OR: 正则条件选择
```

### 逻辑操作符说明
- `;` 代表 **AND（与）** 关系：所有条件都必须满足
- `||` 代表 **OR（或）** 关系：满足任一条件即可
- 可以混合使用：`"条件1;条件2||条件3;条件4"` 表示 `(条件1 AND 条件2) OR (条件3 AND 条件4)`

## 🧪 使用示例

### 基础正则筛选
```bash
# 匹配以192.168开头的IP
python ip_port_checker.py --regex-filter "IP=^192\.168\..*"

# 匹配以GW结尾的网元类型
python ip_port_checker.py --regex-filter "网元类型=.*GW$"

# 匹配以H开头的属地
python ip_port_checker.py --regex-filter "属地=^H.*"
```

### AND逻辑筛选
```bash
# 同时匹配IP和端口（AND关系）
python ip_port_checker.py --regex-filter "IP=^10\..*;端口=^80$"

# 网络类型是5GC或DC，且IP不以17开头
python ip_port_checker.py --regex-filter "网络类型=5GC|DC;IP=^(?!17).*"
```

### OR逻辑筛选
```bash
# 网络类型是5GC或DC（OR关系）
python ip_port_checker.py --regex-filter "网络类型=5GC||网络类型=DC"

# 匹配多个IP网段
python ip_port_checker.py --regex-filter "IP=^192\.168\..*||IP=^10\..*||IP=^172\.16\..*"

# 匹配多个端口
python ip_port_checker.py --regex-filter "端口=^80$||端口=^443$||端口=^22$"
```

### 组合筛选
```bash
# 普通筛选 + 正则筛选
python ip_port_checker.py --filter "协议=TCP" --regex-filter "IP=^192\.168\..*"

# 复杂组合条件
python ip_port_checker.py --filter "网络类型=5GC" --regex-filter "属地=^H.*;IP=^10\..*"
```

### 文本文件模式
```bash
# 文本文件支持IP、端口、协议、网络类型的正则筛选
python ip_port_checker.py -i ip_list.txt -t text --regex-filter "IP=^172\..*"
python ip_port_checker.py -i ip_list.txt -t text --filter "协议=UDP" --regex-filter "IP=^192\.168\..*"

# 文本文件OR逻辑示例
python ip_port_checker.py -i ip_list.txt -t text --regex-filter "网络类型=5GC||网络类型=DC"
```

## 🔀 AND/OR逻辑详解

### AND逻辑（`;` 分隔符）
使用分号 `;` 连接的条件表示"与"关系，**所有条件都必须同时满足**。

```bash
# 示例：网络类型匹配5GC或DC，且IP不以17开头
--regex-filter "网络类型=5GC|DC;IP=^(?!17).*"

# 解释：
# 条件1: 网络类型=5GC|DC  (网络类型是5GC或DC)
# 条件2: IP=^(?!17).*     (IP不以17开头)
# 结果：同时满足条件1和条件2的记录
```

### OR逻辑（`||` 分隔符）
使用双竖线 `||` 连接的条件表示"或"关系，**满足任一条件即可**。

```bash
# 示例：网络类型是5GC或网络类型是DC
--regex-filter "网络类型=5GC||网络类型=DC"

# 解释：
# 条件组1: 网络类型=5GC
# 条件组2: 网络类型=DC
# 结果：满足条件组1或条件组2的记录
```

### 混合逻辑
可以在同一个表达式中混合使用AND和OR逻辑：

```bash
# 示例：(网络类型=5GC且IP以192.168开头) 或 (网络类型=DC且IP以10开头)
--regex-filter "网络类型=5GC;IP=^192\.168\..*||网络类型=DC;IP=^10\..*"

# 解释：
# 条件组1: 网络类型=5GC AND IP=^192\.168\..*
# 条件组2: 网络类型=DC AND IP=^10\..*
# 结果：满足条件组1或条件组2的记录
```

### 逻辑优先级
1. **分号 `;` 优先级更高**：先处理AND关系
2. **双竖线 `||` 优先级较低**：后处理OR关系
3. **执行顺序**：`条件1;条件2||条件3;条件4` = `(条件1 AND 条件2) OR (条件3 AND 条件4)`

## 🔧 常用正则表达式模式

### IP地址筛选
```bash
# 匹配特定网段
"IP=^192\.168\..*"          # 192.168.x.x
"IP=^10\..*"                # 10.x.x.x
"IP=^172\.16\..*"           # 172.16.x.x

# 匹配多个网段
"IP=^(192\.168|10\.0)\..*"  # 192.168.x.x 或 10.0.x.x

# 匹配特定IP范围
"IP=^192\.168\.1\.[1-9]$"   # ***********-***********
```

### 端口筛选
```bash
# 匹配特定端口
"端口=^80$"                 # 精确匹配80端口
"端口=^(80|443|22)$"        # 匹配80、443、22端口

# 匹配端口范围
"端口=^[1-9][0-9]{3}$"      # 1000-9999端口
"端口=^80[0-9]{2}$"         # 8000-8099端口
```

### 网元类型筛选
```bash
# 匹配以特定字符结尾
"网元类型=.*GW$"            # 以GW结尾
"网元类型=.*SERVER$"        # 以SERVER结尾

# 匹配包含特定字符
"网元类型=.*CORE.*"         # 包含CORE
```

### 属地筛选
```bash
# 匹配特定省份
"属地=^H.*"                 # H开头（如HBE、HNY等）
"属地=^(HBE|HNY|HNI)$"      # 精确匹配多个属地
```

## ⚠️ 注意事项

### 1. 正则表达式语法
- 使用标准的正则表达式语法
- 特殊字符需要转义（如 `\.` 表示字面意思的点）
- 语法错误会被自动检测并提示

### 2. 文件类型支持
- **Excel文件**：支持所有列的正则筛选
- **文本文件**：支持IP、端口、协议的正则筛选

### 3. 性能考虑
- 正则表达式筛选比普通筛选稍慢
- 复杂的正则表达式可能影响性能
- 建议先用 `--list-only` 验证筛选结果

## 🚀 实际应用场景

### 场景1：网络安全检查
```bash
# 检查内网IP的特定端口
python ip_port_checker.py --regex-filter "IP=^(192\.168|10\.0|172\.16)\..*;端口=^(22|3389)$"
```

### 场景2：服务器端口检查
```bash
# 检查Web服务端口
python ip_port_checker.py --regex-filter "端口=^(80|443|8080|8443)$"
```

### 场景3：区域性测试
```bash
# 检查特定区域的5G核心网设备
python ip_port_checker.py --filter "网络类型=5GC" --regex-filter "属地=^H.*;网元类型=.*CORE.*"
```

## 🔍 调试和验证

### 使用 --list-only 预览
```bash
# 先预览筛选结果，确认无误后再执行测试
python ip_port_checker.py --regex-filter "IP=^192\.168\..*" --list-only
```

### 错误处理
- 正则表达式语法错误会被自动检测
- 无效的筛选条件会被跳过
- 详细的错误信息帮助调试

## 📊 输出说明

筛选过程中会显示：
- 🔍 正则表达式解析信息
- 📊 每个筛选条件的匹配数量
- 📋 最终筛选结果统计

示例输出：
```
🔍 解析正则表达式筛选: IP ~ /^192\.168\..*/
🔍 应用筛选条件:
  🔍 IP 正则匹配 /^192\.168\..*/: 匹配 4 条记录
  📊 筛选结果: 12 → 4 条记录
```

---

💡 **提示**：正则表达式功能让您的IP端口测试更加精确和灵活，充分利用这个功能可以大大提高工作效率！
