#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试结果管理工具
用于管理按属地分组的测试结果文件
"""

import os
import shutil
import argparse
from datetime import datetime, timedelta
import glob

def list_groups():
    """
    列出所有按属地分组的文件夹
    """
    result_dir = "check-result"
    if not os.path.exists(result_dir):
        print("❌ check-result 目录不存在")
        return

    print("📁 按属地分组文件夹列表:")
    groups = []
    for item in os.listdir(result_dir):
        if item.startswith('group_by_region_') or item.startswith('按属地分组_'):
            item_path = os.path.join(result_dir, item)
            if os.path.isdir(item_path):
                file_count = len([f for f in os.listdir(item_path) if f.endswith('.xlsx')])
                groups.append((item, file_count))
                print(f"  📁 {item}: {file_count} 个文件")

    if not groups:
        print("  🔍 未找到按属地分组文件夹")
    else:
        print(f"\n📊 总计: {len(groups)} 个分组文件夹，{sum(count for _, count in groups)} 个文件")

def list_files_by_group(group_name):
    """
    列出指定分组文件夹的所有文件
    """
    group_dir = os.path.join("check-result", group_name)
    if not os.path.exists(group_dir):
        print(f"❌ 分组文件夹 '{group_name}' 不存在")
        return

    print(f"📄 {group_name} 分组文件列表:")
    files = [f for f in os.listdir(group_dir) if f.endswith('.xlsx')]

    if not files:
        print("  🔍 未找到Excel文件")
        return

    files.sort()
    for file in files:
        file_path = os.path.join(group_dir, file)
        size = os.path.getsize(file_path)
        mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
        print(f"  📄 {file}")
        print(f"      📊 大小: {size:,} 字节")
        print(f"      🕒 修改时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")

def clean_old_files(days=7, dry_run=True):
    """
    清理指定天数前的文件
    """
    result_dir = "check-result"
    if not os.path.exists(result_dir):
        print("❌ check-result 目录不存在")
        return
    
    cutoff_date = datetime.now() - timedelta(days=days)
    print(f"🧹 清理 {days} 天前的文件 (截止日期: {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')})")
    
    if dry_run:
        print("🔍 预览模式 (不会实际删除文件)")
    
    deleted_count = 0
    total_size = 0
    
    # 遍历所有分组文件夹
    for item in os.listdir(result_dir):
        item_path = os.path.join(result_dir, item)
        if os.path.isdir(item_path) and (item.startswith('group_by_region_') or item.startswith('按属地分组_')):
            print(f"\n📁 检查分组: {item}")

            for file in os.listdir(item_path):
                if file.endswith('.xlsx'):
                    file_path = os.path.join(item_path, file)
                    mtime = datetime.fromtimestamp(os.path.getmtime(file_path))

                    if mtime < cutoff_date:
                        size = os.path.getsize(file_path)
                        total_size += size
                        deleted_count += 1

                        print(f"  🗑️  {file} ({mtime.strftime('%Y-%m-%d %H:%M:%S')})")

                        if not dry_run:
                            os.remove(file_path)
                            print(f"      ✅ 已删除")
    
    # 检查根目录下的文件
    print(f"\n📁 检查根目录")
    for file in glob.glob(os.path.join(result_dir, "*.xlsx")):
        mtime = datetime.fromtimestamp(os.path.getmtime(file))
        if mtime < cutoff_date:
            size = os.path.getsize(file)
            total_size += size
            deleted_count += 1
            
            print(f"  🗑️  {os.path.basename(file)} ({mtime.strftime('%Y-%m-%d %H:%M:%S')})")
            
            if not dry_run:
                os.remove(file)
                print(f"      ✅ 已删除")
    
    print(f"\n📊 清理统计:")
    print(f"  📄 文件数量: {deleted_count}")
    print(f"  💾 释放空间: {total_size:,} 字节 ({total_size/1024/1024:.2f} MB)")
    
    if dry_run and deleted_count > 0:
        print(f"\n💡 要实际删除这些文件，请使用: --clean {days} --no-dry-run")

def backup_region(region, backup_dir="backup"):
    """
    备份指定属地的文件
    """
    region_dir = os.path.join("check-result", region)
    if not os.path.exists(region_dir):
        print(f"❌ 属地文件夹 '{region}' 不存在")
        return
    
    # 创建备份目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = os.path.join(backup_dir, f"{region}_{timestamp}")
    
    try:
        shutil.copytree(region_dir, backup_path)
        file_count = len([f for f in os.listdir(backup_path) if f.endswith('.xlsx')])
        print(f"✅ 备份完成: {region} → {backup_path}")
        print(f"📄 备份文件数量: {file_count}")
    except Exception as e:
        print(f"❌ 备份失败: {str(e)}")

def main():
    parser = argparse.ArgumentParser(
        description='📁 测试结果管理工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
📋 使用示例:
  python manage_results.py --list                          # 列出所有属地
  python manage_results.py --region HBE                    # 查看湖北属地文件
  python manage_results.py --clean 7                       # 预览清理7天前的文件
  python manage_results.py --clean 7 --no-dry-run         # 实际清理7天前的文件
  python manage_results.py --backup HBE                    # 备份湖北属地文件
        """
    )
    
    parser.add_argument('--list', '-l',
                       action='store_true',
                       help='📁 列出所有按属地分组文件夹')

    parser.add_argument('--group', '-g',
                       help='📍 查看指定分组文件夹的文件列表')
    
    parser.add_argument('--clean', '-c',
                       type=int,
                       help='🧹 清理指定天数前的文件')
    
    parser.add_argument('--no-dry-run',
                       action='store_true',
                       help='🚨 实际执行清理操作（默认为预览模式）')
    
    parser.add_argument('--backup', '-b',
                       help='💾 备份指定属地的文件')
    
    args = parser.parse_args()
    
    if args.list:
        list_groups()
    elif args.group:
        list_files_by_group(args.group)
    elif args.clean is not None:
        clean_old_files(args.clean, dry_run=not args.no_dry_run)
    elif args.backup:
        backup_region(args.backup)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
