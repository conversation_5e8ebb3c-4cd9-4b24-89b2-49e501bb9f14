#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查Excel输出文件的内容
"""

import pandas as pd
import os

def check_latest_excel():
    """
    检查最新生成的Excel文件
    """
    result_dir = "check-result"
    
    # 找到最新的Excel文件
    excel_files = []
    for file in os.listdir(result_dir):
        if file.endswith('.xlsx') and '全国ip端口_测试结果_' in file:
            excel_files.append(file)
    
    if not excel_files:
        print("❌ 没有找到测试结果文件")
        return
    
    # 按文件名排序，取最新的
    latest_file = sorted(excel_files)[-1]
    file_path = os.path.join(result_dir, latest_file)
    
    print(f"📂 检查文件: {latest_file}")
    print("=" * 60)
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path, engine="openpyxl")
        
        print(f"📊 数据形状: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        print()
        
        # 显示所有数据
        print("📄 完整数据内容:")
        print("-" * 60)
        
        # 设置pandas显示选项
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', 50)
        
        print(df.to_string(index=False))
        
        print("\n" + "=" * 60)
        
        # 检查SNMP输出结果列
        if 'SNMP输出结果' in df.columns:
            print("✅ 成功添加了'SNMP输出结果'列")
            print("\n📡 SNMP输出结果详情:")
            for i, row in df.iterrows():
                protocol = row['协议']
                snmp_output = row['SNMP输出结果']
                # 处理NaN值，将其显示为空白
                if pd.isna(snmp_output):
                    snmp_display = "(空白)"
                else:
                    snmp_display = snmp_output
                print(f"  {i+1}. {row['IP']}:{row['端口']} ({protocol}) -> {snmp_display}")
        else:
            print("❌ 未找到'SNMP输出结果'列")
            
    except Exception as e:
        print(f"❌ 读取文件失败: {str(e)}")

if __name__ == "__main__":
    check_latest_excel()
