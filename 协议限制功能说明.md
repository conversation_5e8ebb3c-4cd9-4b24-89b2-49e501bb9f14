# 🎯 协议限制功能说明

## 📋 功能概述

新增了 `--protocol-only` (简写 `-p`) 参数，支持仅测试指定协议（TCP 或 UDP）的端口连通性，忽略数据文件中的协议列设置。

## 🔧 使用方法

### 基本语法
```bash
python ip_port_checker.py --protocol-only <协议>
python ip_port_checker.py -p <协议>
```

### 支持的协议
- `tcp` 或 `TCP` - 仅测试TCP协议
- `udp` 或 `UDP` - 仅测试UDP协议

## 📝 使用示例

### 1. 仅测试TCP协议
```bash
# 测试Excel文件中的所有IP端口，但只使用TCP协议
python ip_port_checker.py --protocol-only tcp

# 简写形式
python ip_port_checker.py -p tcp
```

### 2. 仅测试UDP协议
```bash
# 测试Excel文件中的所有IP端口，但只使用UDP协议
python ip_port_checker.py --protocol-only udp

# 简写形式
python ip_port_checker.py -p udp
```

### 3. 结合其他筛选条件
```bash
# 仅测试TCP协议，并筛选5GC网络类型
python ip_port_checker.py -p tcp --filter "网络类型=5GC"

# 仅测试UDP协议，使用正则表达式筛选IP
python ip_port_checker.py -p udp --regex-filter "IP=^192\.168\..*"

# 测试文本文件，仅使用TCP协议
python ip_port_checker.py -i test_protocol_only.txt -t text -p tcp
```

### 4. 查看测试列表
```bash
# 查看将要测试的列表（仅TCP协议）
python ip_port_checker.py -p tcp --list-only

# 限制显示前10条记录
python ip_port_checker.py -p udp --list-only --limit 10
```

## 🎯 功能特点

### 1. 协议覆盖
- 当使用 `--protocol-only` 参数时，程序会忽略数据文件中的协议列
- 所有IP端口都将使用指定的协议进行测试
- 原始协议信息仍会保留在结果文件中

### 2. 结果标识
- 测试结果中会新增 `实际测试协议` 列，显示实际使用的协议
- 便于区分原始数据协议和实际测试协议

### 3. 统计信息
- 在测试列表显示时，协议统计会显示强制使用的协议
- 明确标识所有端口都将使用指定协议测试

### 4. 兼容性
- 与现有的所有筛选功能完全兼容
- 支持Excel文件和文本文件两种输入格式
- 支持按属地分组输出等所有现有功能

## 📊 输出示例

### 控制台输出
```
🎯 协议限制模式: 仅测试 TCP 协议
📋 将忽略数据中的协议列，所有端口都使用 TCP 协议进行测试

📈 协议统计 (强制使用 TCP):
  🔗 TCP: 8 个 (所有端口都将使用此协议测试)

🚀 开始测试 8 个IP端口...
⏱️  超时时间: 8秒, 🔄 并发数: 50
🎯 协议限制: 所有端口都将使用 TCP 协议进行测试
```

### Excel结果文件
结果文件会包含以下列：
- `IP` - IP地址
- `端口` - 端口号
- `协议` - 原始数据中的协议
- `实际测试协议` - 实际使用的测试协议（新增）
- `状态` - 连通性状态
- `测试开始时间` - 测试时间
- `路由信息` - 路由详情
- `SNMP输出结果` - UDP测试的SNMP结果

## 💡 使用场景

1. **网络故障排查** - 怀疑某个协议有问题时，可以专门测试该协议
2. **性能对比** - 对比同一组IP端口在TCP和UDP协议下的连通性差异
3. **批量协议转换** - 将混合协议的测试列表统一转换为单一协议测试
4. **合规性检查** - 某些环境只允许特定协议，需要验证连通性

## ⚠️ 注意事项

1. **UDP测试依赖** - UDP协议测试需要系统安装SNMP工具包
2. **协议适用性** - 确保目标服务支持指定的协议（如HTTP服务用TCP，DNS服务用UDP）
3. **结果解读** - 测试结果反映的是指定协议的连通性，可能与实际服务协议不符
4. **性能影响** - UDP测试通常比TCP测试耗时更长

## 🔄 与现有功能的关系

- `--protocol-only` 参数优先级高于数据文件中的协议列
- 可以与 `--filter`、`--regex-filter` 等筛选参数组合使用
- 支持 `--list-only` 预览功能
- 兼容 `--timeout`、`--workers` 等性能参数
- 支持按属地分组输出（Excel文件）
