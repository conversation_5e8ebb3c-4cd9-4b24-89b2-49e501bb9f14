#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
路由追踪测试脚本 - 专为K8s Pod环境设计
测试针对不同IP的路由追踪功能
"""

import subprocess
import sys
import os

def test_route_for_ip(target_ip):
    """
    测试获取指定IP的路由信息
    """
    print(f"\n🔍 测试IP: {target_ip}")
    print("-" * 50)
    
    try:
        # Linux环境 - K8s Pod中常用
        if os.name != 'nt':
            print("📍 使用Linux ip route命令:")
            result = subprocess.run(['ip', 'route', 'get', target_ip], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ 原始输出: {result.stdout.strip()}")
                route_info = parse_linux_route(result.stdout.strip(), target_ip)
                print(f"📋 解析结果: {route_info}")
            else:
                print(f"❌ 命令失败: {result.stderr}")
        
        # Windows环境
        else:
            print("📍 使用Windows route命令:")
            result = subprocess.run(['route', 'print', '-4'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                route_info = parse_windows_route(result.stdout, target_ip)
                print(f"📋 解析结果: {route_info}")
            else:
                print(f"❌ 命令失败")
                
    except Exception as e:
        print(f"❌ 异常: {str(e)}")

def parse_linux_route(route_output, target_ip):
    """
    解析Linux ip route get命令的输出
    """
    try:
        # 示例输出: "***********00 via *********** dev eth0 src ************ uid 1000"
        parts = route_output.split()
        
        gateway = "直连"
        interface = "未知"
        src_ip = "未知"
        
        for i, part in enumerate(parts):
            if part == "via" and i + 1 < len(parts):
                gateway = parts[i + 1]
            elif part == "dev" and i + 1 < len(parts):
                interface = parts[i + 1]
            elif part == "src" and i + 1 < len(parts):
                src_ip = parts[i + 1]
        
        # 格式化路由信息
        if gateway == "直连":
            return f"直连({interface})"
        else:
            return f"网关:{gateway} 网卡:{interface} 源IP:{src_ip}"
            
    except Exception as e:
        return f"解析失败: {str(e)}"

def parse_windows_route(route_output, target_ip):
    """
    解析Windows route print命令的输出
    """
    try:
        import ipaddress
        target = ipaddress.IPv4Address(target_ip)
        
        lines = route_output.split('\n')
        best_match = None
        best_prefix_len = -1
        
        # 查找IPv4路由表部分
        in_ipv4_table = False
        for line in lines:
            line = line.strip()
            
            if "IPv4 路由表" in line or "IPv4 Route Table" in line:
                in_ipv4_table = True
                continue
            elif "IPv6 路由表" in line or "IPv6 Route Table" in line:
                in_ipv4_table = False
                continue
            
            if not in_ipv4_table:
                continue
            
            # 解析路由表行
            parts = line.split()
            if len(parts) >= 4:
                try:
                    network_dest = parts[0]
                    netmask = parts[1]
                    gateway = parts[2]
                    interface = parts[3]
                    
                    # 检查是否为有效的IP地址
                    if not is_valid_ip(network_dest) or not is_valid_ip(netmask):
                        continue
                    
                    # 计算网络前缀长度
                    network = ipaddress.IPv4Network(f"{network_dest}/{netmask}", strict=False)
                    
                    # 检查目标IP是否在此网络中
                    if target in network:
                        prefix_len = network.prefixlen
                        if prefix_len > best_prefix_len:
                            best_prefix_len = prefix_len
                            best_match = {
                                'gateway': gateway,
                                'interface': interface,
                                'network': str(network)
                            }
                except:
                    continue
        
        if best_match:
            if best_match['gateway'] == best_match['interface']:
                return f"直连({best_match['interface']})"
            else:
                return f"网关:{best_match['gateway']} 接口:{best_match['interface']}"
        
        return "未找到匹配路由"
        
    except Exception as e:
        return f"解析失败: {str(e)}"

def is_valid_ip(ip):
    """
    验证IP地址格式
    """
    try:
        parts = ip.split('.')
        if len(parts) != 4:
            return False
        for part in parts:
            if not (0 <= int(part) <= 255):
                return False
        return True
    except:
        return False

def main():
    """
    主测试函数
    """
    print("🌐" + "=" * 58 + "🌐")
    print("🔍 路由追踪测试工具 - K8s Pod环境优化版 🔍")
    print("🌐" + "=" * 58 + "🌐")
    
    # 检测运行环境
    print(f"\n📋 运行环境信息:")
    print(f"  🖥️  操作系统: {os.name}")
    print(f"  🐍 Python版本: {sys.version}")
    
    # 检测是否在容器中
    if os.path.exists('/.dockerenv'):
        print(f"  🐳 检测到Docker容器环境")
    
    if os.path.exists('/var/run/secrets/kubernetes.io'):
        print(f"  ☸️  检测到Kubernetes Pod环境")
    
    # 测试不同类型的IP
    test_ips = [
        "*******",          # 公网DNS
        "***********",      # 私网网关
        "********",         # 私网
        "**********",       # 私网
        "**********",       # 测试数据中的IP
    ]
    
    print(f"\n🚀 开始测试不同IP的路由信息...")
    
    for ip in test_ips:
        test_route_for_ip(ip)
    
    print(f"\n✅ 测试完成！")
    print(f"\n💡 在K8s Pod中运行时，路由信息会显示Pod的网络视图")
    print(f"   这可能与宿主机的路由表不同")

if __name__ == "__main__":
    main()
