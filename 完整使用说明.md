# 🌐 IP端口连通性测试工具 - 完整使用说明

## 📋 功能描述
这是一个用于批量测试IP端口连通性的Python脚本，支持TCP和UDP协议的连通性测试。

## ✨ 主要特性
- 🔗 支持TCP和UDP协议测试
- ⚡ 多线程并发测试，提高效率
- 📊 自动读取Excel文件并输出结果
- 📈 详细的测试进度显示（带emoji美化）
- 🛡️ 完整的错误处理和异常捕获
- ⚙️ 可配置的超时时间和并发数
- 📅 输出文件自动添加时间戳
- 🛑 支持Ctrl+C优雅中断（Linux环境最佳）
- 🔍 灵活的筛选条件和命令行参数
- 🗺️ 按属地分组输出和指定属地测试
- 📋 测试前预览功能

## 🔧 环境要求
- 🐍 Python 3.6+
- 📦 依赖包：pandas, openpyxl

## 📥 安装依赖
```bash
pip install pandas openpyxl
```

## 🚀 使用方法

### 📁 脚本说明

#### 1️⃣ `test_sample_checker.py` - 样本测试脚本
用于快速测试少量样本，验证功能和筛选条件。

#### 2️⃣ `ip_port_checker.py` - 完整测试脚本  
用于大规模批量测试，支持并发处理。

#### 3️⃣ `accurate_udp_test.py` - UDP测试方法对比脚本
用于对比不同UDP测试方法的结果。

#### 4️⃣ `manage_results.py` - 结果管理工具
用于管理和清理测试结果文件。

### 📖 命令行参数详解

#### 🔍 样本测试脚本参数

```bash
python test_sample_checker.py [参数]
```

| 参数 | 简写 | 类型 | 默认值 | 说明 |
|------|------|------|--------|------|
| `--input-file` | `-i` | 字符串 | `全国ip端口.xlsx` | 📂 输入Excel文件路径 |
| `--tcp-count` | `-t` | 整数 | `5` | 🔗 TCP样本数量 |
| `--udp-count` | `-u` | 整数 | `5` | 📡 UDP样本数量 |
| `--timeout` | | 整数 | `5` | ⏱️ 连接超时时间（秒） |
| `--filter` | `-f` | 字符串 | 默认筛选 | 🔍 筛选条件 |
| `--no-filter` | | 标志 | | 🚫 不使用任何筛选条件 |
| `--list-columns` | | 标志 | | 📋 显示可用列名并退出 |
| `--no-group-by-region` | | 标志 | | 🚫 不按属地分组输出 |
| `--region` | `-r` | 字符串 | | 🗺️ 指定测试某个属地 |
| `--list-only` | | 标志 | | 📋 只输出测试列表，不执行测试 |

#### 🔍 完整测试脚本参数

```bash
python ip_port_checker.py [参数]
```

| 参数 | 简写 | 类型 | 默认值 | 说明 |
|------|------|------|--------|------|
| `--input-file` | `-i` | 字符串 | `全国ip端口.xlsx` | 📂 输入Excel文件路径 |
| `--timeout` | `-t` | 整数 | `3` | ⏱️ 连接超时时间（秒） |
| `--workers` | `-w` | 整数 | `50` | 🔄 最大并发线程数 |
| `--limit` | `-l` | 整数 | 无限制 | 📊 限制测试数据条数 |
| `--filter` | `-f` | 字符串 | 默认筛选 | 🔍 筛选条件 |
| `--no-filter` | | 标志 | | 🚫 不使用任何筛选条件 |
| `--list-columns` | | 标志 | | 📋 显示可用列名并退出 |
| `--no-group-by-region` | | 标志 | | 🚫 不按属地分组输出 |
| `--region` | `-r` | 字符串 | | 🗺️ 指定测试某个属地 |
| `--list-only` | | 标志 | | 📋 只输出测试列表，不执行测试 |

## 🎯 筛选条件语法

### 基本格式
```
"列名=值1,值2"          # 单列多值（或关系）
"列名1=值1;列名2=值2"   # 多列条件（且关系）
```

### 示例
```bash
# 单列筛选
--filter "网元类型=IBCF"
--filter "网元类型=IBCF,FIREWALL"

# 多列筛选
--filter "网元类型=IBCF;属地=HBE"
--filter "网元类型=UPF,DCGW;协议=TCP"
```

## 🎯 默认筛选条件

如果不指定 `--filter` 参数且不使用 `--no-filter`，将使用默认筛选条件：

```
网络类型 = 5GC, DC
```

这些网络类型包括：
- **5GC**: 5G核心网（5G Core）
- **DC**: 数据中心（Data Center）

## 📊 输出文件

### 🏷️ 按属地分组输出（默认启用）

默认情况下，脚本会根据"属地"字段将结果分组保存到不同文件中：

**样本测试：**
- `check-result/group_by_region_YYYYMMDD_HHMMSS/样本测试结果_HBE_YYYYMMDD_HHMMSS.xlsx` (湖北)
- `check-result/group_by_region_YYYYMMDD_HHMMSS/样本测试结果_YND_YYYYMMDD_HHMMSS.xlsx` (云南)
- `check-result/group_by_region_YYYYMMDD_HHMMSS/样本测试结果_HNY_YYYYMMDD_HHMMSS.xlsx` (河南)

**完整测试：**
- `check-result/group_by_region_YYYYMMDD_HHMMSS/全国ip端口_测试结果_HBE_YYYYMMDD_HHMMSS.xlsx`
- `check-result/group_by_region_YYYYMMDD_HHMMSS/全国ip端口_测试结果_YND_YYYYMMDD_HHMMSS.xlsx`
- `check-result/group_by_region_YYYYMMDD_HHMMSS/全国ip端口_测试结果_HNY_YYYYMMDD_HHMMSS.xlsx`

### 📄 单文件输出

使用 `--no-group-by-region` 参数可以将所有结果保存到单个文件：

- 样本测试：`check-result/样本测试结果_YYYYMMDD_HHMMSS.xlsx`
- 完整测试：`check-result/全国ip端口_测试结果_YYYYMMDD_HHMMSS.xlsx`
- UDP对比测试：`check-result/UDP测试方法对比_YYYYMMDD_HHMMSS.xlsx`

📁 如果 `check-result` 目录不存在，脚本会自动创建该目录。

## 📚 使用示例

### 🔍 样本测试示例

```bash
# 1. 查看可用列名
python test_sample_checker.py --list-columns

# 2. 使用默认筛选条件（网络类型=5GC,DC）
python test_sample_checker.py

# 3. 不使用筛选条件，测试全部数据的样本
python test_sample_checker.py --no-filter

# 4. 自定义筛选条件
python test_sample_checker.py --filter "网元类型=IBCF,FIREWALL"

# 5. 设置样本数量
python test_sample_checker.py --tcp-count 10 --udp-count 5

# 6. 设置超时时间
python test_sample_checker.py --timeout 10

# 7. 复合条件筛选
python test_sample_checker.py --filter "网元类型=IBCF;属地=HBE" --tcp-count 3

# 8. 不按属地分组，保存到单个文件
python test_sample_checker.py --no-group-by-region

# 9. 指定测试某个属地
python test_sample_checker.py --region HBE --tcp-count 5

# 10. 只显示测试列表，不执行测试
python test_sample_checker.py --region YND --list-only

# 11. 组合使用：查看特定属地和网元类型的测试列表
python test_sample_checker.py --filter "网元类型=UPF" --region YND --list-only
```

### 🔍 完整测试示例

```bash
# 1. 查看可用列名
python ip_port_checker.py --list-columns

# 2. 使用默认筛选条件（网络类型=5GC,DC）
python ip_port_checker.py

# 3. 不使用筛选条件，测试全部数据
python ip_port_checker.py --no-filter

# 4. 自定义筛选条件，限制测试数量
python ip_port_checker.py --filter "网元类型=IBCF" --limit 100

# 5. 高性能测试配置
python ip_port_checker.py --timeout 2 --workers 100 --limit 1000

# 6. 测试特定地区的设备
python ip_port_checker.py --filter "属地=HBE,YND" --limit 500

# 7. 测试特定协议
python ip_port_checker.py --filter "协议=TCP" --limit 200

# 8. 不按属地分组，保存到单个文件
python ip_port_checker.py --no-group-by-region --limit 100

# 9. 指定测试某个属地
python ip_port_checker.py --region HBE --limit 200

# 10. 只显示测试列表，不执行测试
python ip_port_checker.py --region YND --limit 50 --list-only

# 11. 查看特定条件的测试列表
python ip_port_checker.py --filter "网元类型=UPF;属地=YND" --list-only
```

## 🆕 新功能详解

### 🗺️ 指定属地测试功能

#### 基本用法
```bash
# 只测试湖北属地的设备
python test_sample_checker.py --region HBE

# 只测试云南属地的设备
python ip_port_checker.py --region YND --limit 100
```

#### 属地代码参考

常见的属地代码：
- **HBE**: 湖北
- **YND**: 云南  
- **HNY**: 河南
- **LNL**: 辽宁
- **SHH**: 上海
- **BJJ**: 北京
- **GDD**: 广东
- **ZJJ**: 浙江

### 📋 列表预览模式功能

#### 输出列说明

`--list-only` 模式会智能显示相关的列：

- **基本列**：始终显示 `IP地址`、`端口`、`协议`
- **筛选条件列**：显示筛选条件中涉及的列（如 `网元类型`、`属地` 等）
- **动态宽度**：根据数据内容自动调整列宽

#### 示例对比

```bash
# 无筛选条件：只显示基本列
python test_sample_checker.py --no-filter --list-only
# 输出：序号 | IP地址 | 端口 | 协议

# 有筛选条件：显示基本列 + 筛选条件相关列
python test_sample_checker.py --filter "网元类型=UPF" --region HBE --list-only  
# 输出：序号 | IP地址 | 端口 | 协议 | 网元类型 | 属地
```

#### 实际使用示例

```bash
python test_sample_checker.py --region HBE --tcp-count 5 --udp-count 3 --list-only

# 输出示例：
📋 要测试的IP端口列表:
============================================
序号   IP地址            端口     协议     网元类型   属地
--------------------------------------------
1    **********      8153   TCP    UPF    HBE
2    **********      2022   TCP    UPF    HBE
3    **********      50163  TCP    DCGW   HBE  
4    **********      50163  TCP    DCGW   HBE
5    **********      8153   TCP    UPF    HBE
6    **********      161    UDP    DCGW   HBE
7    **********      161    UDP    DCGW   HBE
8    **********      161    UDP    DCGW   HBE
============================================
📊 总计: 8 个IP端口待测试

📈 协议统计:
  🔗 TCP: 5 个
  📡 UDP: 3 个

📊 网元类型统计:
  📍 DCGW: 6 个
  📍 UPF: 2 个

📊 属地统计:
  📍 HBE: 8 个

💡 要执行实际测试，请去掉 --list-only 参数
```

## 📁 按属地分组功能

### 目录结构
```
check-port/
├── check-result/                    # 输出目录
│   ├── group_by_region_20250722_093641/    # 样本测试的分组文件夹
│   │   └── 样本测试结果_HBE_20250722_093641.xlsx
│   ├── group_by_region_20250722_093741/    # 完整测试的分组文件夹
│   │   ├── 全国ip端口_测试结果_HBE_20250722_093741.xlsx
│   │   ├── 全国ip端口_测试结果_YND_20250722_093741.xlsx
│   │   └── 全国ip端口_测试结果_HNY_20250722_093741.xlsx
│   └── 样本测试结果_20250722_091835.xlsx  # 单文件模式
└── ...
```

### 结果汇总
```bash
# 查看按属地分组的测试结果
ls -la check-result/group_by_region_*/     # 查看所有分组文件夹
ls -la check-result/group_by_region_20250722_093741/  # 查看特定分组的文件

# 统计分组文件夹中的文件数量
find check-result/group_by_region_*/ -name "*.xlsx" | wc -l

# 查看最新的分组文件夹
ls -t check-result/group_by_region_*/ | head -1

# 查看特定属地的所有测试结果
find check-result/group_by_region_*/ -name "*HBE*.xlsx"  # 湖北的所有结果
find check-result/group_by_region_*/ -name "*YND*.xlsx"  # 云南的所有结果
```

## 🎨 示例输出

### 默认分组输出
```bash
# 运行测试
python test_sample_checker.py --filter "网元类型=IBCF" --tcp-count 5

# 输出结果：
📁 按属地分组保存结果...
  📁 创建分组目录: group_by_region_20250722_093641
  📍 HBE: 2 条记录 → 样本测试结果_HBE_20250722_093641.xlsx

💾 共保存 1 个属地文件到: check-result/group_by_region_20250722_093641
  📄 样本测试结果_HBE_20250722_093641.xlsx
```

### 单文件输出
```bash
# 运行测试（不分组）
python test_sample_checker.py --filter "网元类型=IBCF" --tcp-count 5 --no-group-by-region

# 输出结果：
💾 结果已保存到: check-result/样本测试结果_20250722_091835.xlsx
```

## 🔧 结果管理工具

### 基本用法
```bash
# 列出所有分组文件夹
python manage_results.py --list

# 查看特定分组的文件
python manage_results.py --group "group_by_region_20250722_093741"

# 清理旧文件（预览模式）
python manage_results.py --clean 7

# 实际清理7天前的文件
python manage_results.py --clean 7 --no-dry-run

# 备份特定分组
python manage_results.py --backup "group_by_region_20250722_093741"
```

## ⚠️ 注意事项

1. **筛选条件大小写敏感**：确保列名和值的大小写正确
2. **并发数设置**：过高的并发数可能导致网络拥塞或被目标系统限制
3. **超时时间**：过短可能导致误报，过长会影响测试效率
4. **数据量控制**：大规模测试建议使用 `--limit` 参数分批进行
5. **中断处理**：支持 Ctrl+C 优雅中断，在Linux环境下表现最佳
6. **运行环境**：推荐在Linux环境下运行，信号处理更加可靠

## 🔧 故障排除

### 常见错误及解决方案

1. **"列不存在"警告**
   ```bash
   # 先查看可用列名
   python test_sample_checker.py --list-columns
   ```

2. **筛选后无数据**
   ```bash
   # 检查筛选条件是否正确
   python test_sample_checker.py --filter "网元类型=IBCF" --no-filter
   ```

3. **测试速度慢**
   ```bash
   # 减少超时时间，增加并发数
   python ip_port_checker.py --timeout 2 --workers 100
   ```

## 🎉 高级用法

### 批量测试不同条件
```bash
# 测试不同网元类型
python ip_port_checker.py --filter "网元类型=IBCF" --limit 100
python ip_port_checker.py --filter "网元类型=UPF" --limit 100
python ip_port_checker.py --filter "网元类型=DCGW" --limit 100
```

### 性能调优
```bash
# 快速测试（低精度）
python ip_port_checker.py --timeout 1 --workers 200

# 精确测试（高精度）
python ip_port_checker.py --timeout 10 --workers 20
```

### 分步骤测试流程
```bash
# 第1步：预览要测试的内容
python ip_port_checker.py --region HBE --limit 100 --list-only

# 第2步：确认无误后执行实际测试
python ip_port_checker.py --region HBE --limit 100

# 第3步：查看测试结果
ls -la check-result/group_by_region_*/
```

## 💡 最佳实践

1. **分批测试**：大数据集建议使用 `--limit` 分批处理
2. **合理并发**：根据系统性能调整 `--workers` 参数
3. **及时中断**：长时间测试可随时用 Ctrl+C 中断
4. **结果备份**：定期备份 `check-result` 目录
5. **日志记录**：重要测试建议重定向输出到日志文件
6. **测试前预览**：使用 `--list-only` 预览要测试的内容
7. **合理设置限制**：对于大属地，建议设置合理的limit
8. **结合筛选条件**：指定属地 + 网元类型，精确定位

## 🎯 性能参考

| 配置 | 并发数 | 超时 | 适用场景 |
|------|--------|------|----------|
| 快速 | 100-200 | 1-2秒 | 快速验证 |
| 平衡 | 50-100 | 3-5秒 | 日常测试 |
| 精确 | 10-30 | 5-10秒 | 重要验证 |

在Linux环境下，这些配置都能很好地支持Ctrl+C中断！🐧

---

**IP端口连通性测试工具让您的网络设备管理更加高效和有序！** 🎉
