# IP端口连通性测试工具 - 文本文件模式使用说明

## 概述

IP端口连通性测试工具现已支持文本文件格式输入，可以直接测试您提供的文本格式的IP端口列表。

## 文本文件格式要求

文本文件应按以下格式组织，每行一个IP端口记录：

```
IP地址 端口 协议
```

### 示例文件内容：

```
# IP端口测试列表
# 格式：IP地址 端口 协议
************     22     tcp
************     22     tcp
************     22     tcp
************     22     tcp
************   161     udp
************   161     udp
************   161     udp
************   161     udp
```

### 格式说明：

1. **分隔符**：使用空格分隔IP地址、端口和协议
2. **协议**：支持 `tcp`、`udp`（大小写不敏感）
3. **注释**：以 `#` 开头的行会被忽略
4. **空行**：空行会被自动跳过
5. **端口**：必须是数字
6. **IP地址**：支持IPv4格式

## 使用方法

### 1. 自动检测文件类型（推荐）

```bash
python ip_port_checker.py -i your_ip_list.txt
```

工具会根据文件扩展名自动检测为文本格式。

### 2. 明确指定文本文件类型

```bash
python ip_port_checker.py -i your_ip_list.txt --input-type text
```

### 3. 只查看测试列表（不执行测试）

```bash
python ip_port_checker.py -i your_ip_list.txt --list-only
```

### 4. 筛选特定协议

```bash
# 只测试TCP协议
python ip_port_checker.py -i your_ip_list.txt --filter "协议=TCP"

# 只测试UDP协议
python ip_port_checker.py -i your_ip_list.txt --filter "协议=UDP"
```

### 5. 限制测试数量

```bash
# 只测试前10个IP端口
python ip_port_checker.py -i your_ip_list.txt --limit 10
```

### 6. 调整超时和并发设置

```bash
# 设置5秒超时，100个并发线程
python ip_port_checker.py -i your_ip_list.txt --timeout 5 --workers 100
```

## 文本文件模式特点

### 支持的功能：
- ✅ 自动检测文件类型
- ✅ TCP/UDP连通性测试
- ✅ 协议筛选
- ✅ 限制测试数量
- ✅ 调整超时和并发设置
- ✅ 测试结果保存为Excel文件

### 不支持的功能：
- ❌ 按属地分组（文本文件没有属地信息）
- ❌ 复杂筛选条件（只支持协议筛选）
- ❌ 列名查看（文本文件列名固定）

## 输出结果

测试结果会保存在 `check-result` 目录下，文件名格式为：
```
全国ip端口_测试结果_YYYYMMDD_HHMMSS.xlsx
```

结果文件包含以下列：
- IP：IP地址
- 端口：端口号
- 协议：协议类型（TCP/UDP）
- 状态：连通性状态（连通/不连通）
- 测试开始时间：测试执行时间

## 常见问题

### Q: 文本文件格式错误怎么办？
A: 工具会自动跳过格式错误的行，并在控制台显示警告信息。请确保每行按照 "IP 端口 协议" 的格式组织。

### Q: 支持哪些文件扩展名？
A: 支持 `.txt`、`.text`、`.dat`、`.csv` 等文本文件扩展名。

### Q: 可以混合TCP和UDP测试吗？
A: 可以，工具会根据每行指定的协议类型进行相应的连通性测试。

### Q: UDP测试准确吗？
A: UDP是无连接协议，工具使用"准确检测"方法：发送探测包并等待响应，只有收到响应才认为端口连通。结果仅供参考。

## 完整示例

创建测试文件 `test_servers.txt`：
```
***********    22    tcp
***********    80    tcp
***********    443   tcp
***********    161   udp
***********    53    udp
```

执行测试：
```bash
python ip_port_checker.py -i test_servers.txt --timeout 5
```

这将测试所有5个IP端口的连通性，超时时间设置为5秒。
