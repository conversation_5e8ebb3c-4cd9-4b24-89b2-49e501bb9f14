#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
IP端口连通性测试脚本
支持TCP和UDP协议的连通性测试

UDP测试方法：使用SNMP v3进行连通性测试
测试命令：snmpget -v 3 -u test -l authPriv -a MD5 -A 'testauthPriv' -x AES -X 'testsuppPriv' ip:端口 *******.4.1.2011.**********.2.2.0
要求：系统需安装snmpget工具（net-snmp或snmp-utils包）
"""

import pandas as pd
import socket
import threading
import time
from datetime import datetime
import sys
import os
import argparse
import signal
import subprocess
import re
from concurrent.futures import ThreadPoolExecutor, as_completed

# 全局中断标志
interrupted = False

class PortChecker:
    def __init__(self, timeout=8, max_workers=50):
        """
        初始化端口检查器

        Args:
            timeout (int): 连接超时时间（秒，默认8秒）
            max_workers (int): 最大并发线程数
        """
        self.timeout = timeout
        self.max_workers = max_workers
        self.default_gateway = self.get_default_gateway()

    def get_route_for_ip(self, target_ip):
        """
        获取到达指定IP的具体路由信息（网卡、网关等）

        Args:
            target_ip (str): 目标IP地址

        Returns:
            str: 路由信息字符串，包含网关和网卡信息
        """
        try:
            # 优先使用Linux的ip route命令（适用于K8s Pod环境）
            if os.name != 'nt':  # Linux/Unix系统
                result = subprocess.run(['ip', 'route', 'get', target_ip],
                                      stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, timeout=5)
                if result.returncode == 0:
                    output = result.stdout.strip()
                    # 解析ip route get输出
                    # 示例输出: "************* via *********** dev eth0 src ************"
                    route_info = self._parse_linux_route(output, target_ip)
                    if route_info:
                        return route_info

            # Windows系统的路由查询
            else:
                # 使用route print命令查找特定目标的路由
                result = subprocess.run(['route', 'print', '-4'],
                                      stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, timeout=10)
                if result.returncode == 0:
                    route_info = self._parse_windows_route(result.stdout, target_ip)
                    if route_info:
                        return route_info

        except Exception as e:
            print(f"⚠️ 获取{target_ip}的路由信息失败: {str(e)}")

        return f"未知路由"

    def _parse_linux_route(self, route_output, target_ip):
        """
        解析Linux ip route get命令的输出

        Args:
            route_output (str): ip route get命令的输出
            target_ip (str): 目标IP

        Returns:
            str: 格式化的路由信息
        """
        try:
            # 示例输出: "************* via *********** dev eth0 src ************ uid 1000"
            parts = route_output.split()

            gateway = "直连"
            interface = "未知"
            src_ip = "未知"

            for i, part in enumerate(parts):
                if part == "via" and i + 1 < len(parts):
                    gateway = parts[i + 1]
                elif part == "dev" and i + 1 < len(parts):
                    interface = parts[i + 1]
                elif part == "src" and i + 1 < len(parts):
                    src_ip = parts[i + 1]

            # 格式化路由信息
            if gateway == "直连":
                return f"直连({interface})"
            else:
                return f"网关:{gateway} 网卡:{interface} 源IP:{src_ip}"

        except Exception as e:
            print(f"⚠️ 解析Linux路由输出失败: {str(e)}")
            return "解析失败"

    def _parse_windows_route(self, route_output, target_ip):
        """
        解析Windows route print命令的输出，查找目标IP的最佳路由

        Args:
            route_output (str): route print命令的输出
            target_ip (str): 目标IP

        Returns:
            str: 格式化的路由信息
        """
        try:
            import ipaddress
            target = ipaddress.IPv4Address(target_ip)

            lines = route_output.split('\n')
            best_match = None
            best_prefix_len = -1

            # 查找IPv4路由表部分
            in_ipv4_table = False
            for line in lines:
                line = line.strip()

                if "IPv4 路由表" in line or "IPv4 Route Table" in line:
                    in_ipv4_table = True
                    continue
                elif "IPv6 路由表" in line or "IPv6 Route Table" in line:
                    in_ipv4_table = False
                    continue

                if not in_ipv4_table:
                    continue

                # 解析路由表行
                parts = line.split()
                if len(parts) >= 4:
                    try:
                        network_dest = parts[0]
                        netmask = parts[1]
                        gateway = parts[2]
                        interface = parts[3]

                        # 检查是否为有效的IP地址
                        if not self._is_valid_ip(network_dest) or not self._is_valid_ip(netmask):
                            continue

                        # 计算网络前缀长度
                        network = ipaddress.IPv4Network(f"{network_dest}/{netmask}", strict=False)

                        # 检查目标IP是否在此网络中
                        if target in network:
                            prefix_len = network.prefixlen
                            if prefix_len > best_prefix_len:
                                best_prefix_len = prefix_len
                                best_match = {
                                    'gateway': gateway,
                                    'interface': interface,
                                    'network': str(network)
                                }
                    except:
                        continue

            if best_match:
                if best_match['gateway'] == best_match['interface']:
                    return f"直连({best_match['interface']})"
                else:
                    return f"网关:{best_match['gateway']} 接口:{best_match['interface']}"

        except Exception as e:
            print(f"⚠️ 解析Windows路由输出失败: {str(e)}")

        return "路由解析失败"

    def get_default_gateway(self):
        """
        获取系统默认网关地址，优先选择最佳网关

        Returns:
            str: 默认网关IP地址，获取失败时返回"未知"
        """
        try:
            # Windows系统获取默认网关
            if os.name == 'nt':
                gateways = []

                # 使用route print命令获取所有默认网关
                result = subprocess.run(['route', 'print', '0.0.0.0'],
                                      stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, timeout=10)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if '0.0.0.0' in line:
                            parts = line.split()
                            if len(parts) >= 5 and parts[0] == '0.0.0.0' and parts[1] == '0.0.0.0':
                                gateway = parts[2]
                                interface = parts[3]
                                metric = int(parts[4]) if parts[4].isdigit() else 99999

                                # 验证IP格式
                                if self._is_valid_ip(gateway):
                                    gateways.append({
                                        'gateway': gateway,
                                        'interface': interface,
                                        'metric': metric
                                    })

                # 如果找到多个网关，选择最佳的
                if gateways:
                    # 按跃点数排序，选择跃点数最小的
                    gateways.sort(key=lambda x: x['metric'])
                    best_gateway = gateways[0]

                    # 如果有多个网关，显示所有网关信息
                    if len(gateways) > 1:
                        print(f"🔍 检测到多个默认网关:")
                        for gw in gateways:
                            print(f"  📍 {gw['gateway']} (接口: {gw['interface']}, 跃点数: {gw['metric']})")
                        print(f"  ✅ 选择最佳网关: {best_gateway['gateway']} (跃点数最小)")

                    return best_gateway['gateway']

                # 备用方法：使用ipconfig命令查找活动网关
                result = subprocess.run(['ipconfig'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, timeout=10)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    current_adapter = ""
                    adapter_gateways = []

                    for line in lines:
                        line = line.strip()
                        # 检测适配器名称
                        if '适配器' in line and ':' in line:
                            current_adapter = line
                        # 查找默认网关
                        elif ('默认网关' in line or 'Default Gateway' in line) and ':' in line:
                            ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', line)
                            if ip_match:
                                gateway = ip_match.group(1)
                                # 过滤掉明显的VPN/虚拟网络网关
                                if not self._is_virtual_gateway(gateway):
                                    adapter_gateways.append({
                                        'gateway': gateway,
                                        'adapter': current_adapter
                                    })

                    if adapter_gateways:
                        # 优先选择看起来像本地网络的网关
                        for gw in adapter_gateways:
                            if self._is_local_network_gateway(gw['gateway']):
                                return gw['gateway']
                        # 如果没有明显的本地网关，返回第一个
                        return adapter_gateways[0]['gateway']

            else:
                # Linux/Unix系统获取默认网关
                result = subprocess.run(['ip', 'route', 'show', 'default'],
                                      stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, timeout=10)
                if result.returncode == 0:
                    match = re.search(r'default via (\d+\.\d+\.\d+\.\d+)', result.stdout)
                    if match:
                        return match.group(1)

                # 备用方法：使用route命令
                result = subprocess.run(['route', '-n'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, timeout=10)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if line.startswith('0.0.0.0'):
                            parts = line.split()
                            if len(parts) >= 2:
                                gateway = parts[1]
                                if self._is_valid_ip(gateway):
                                    return gateway
        except Exception as e:
            print(f"⚠️ 获取默认网关失败: {str(e)}")

        return "未知"

    def _is_virtual_gateway(self, gateway):
        """
        判断是否为虚拟网络网关
        """
        # 常见的VPN/虚拟网络网关特征
        virtual_patterns = [
            '**************',  # ZeroTier
            '********',        # 常见VPN
            '**********',      # 常见VPN
        ]
        return gateway in virtual_patterns

    def _is_local_network_gateway(self, gateway):
        """
        判断是否为本地网络网关
        """
        # 常见的本地网络网关模式
        local_patterns = [
            r'^192\.168\.\d+\.1$',      # 192.168.x.1
            r'^10\.\d+\.\d+\.1$',       # 10.x.x.1
            r'^172\.(1[6-9]|2[0-9]|3[01])\.\d+\.1$',  # 172.16-31.x.1
        ]
        for pattern in local_patterns:
            if re.match(pattern, gateway):
                return True
        return False

    def _is_valid_ip(self, ip):
        """
        验证IP地址格式是否正确

        Args:
            ip (str): IP地址字符串

        Returns:
            bool: True表示格式正确，False表示格式错误
        """
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                if not (0 <= int(part) <= 255):
                    return False
            return True
        except:
            return False

    def check_tcp_port(self, ip, port):
        """
        检查TCP端口连通性
        
        Args:
            ip (str): IP地址
            port (int): 端口号
            
        Returns:
            bool: True表示连通，False表示不连通
        """
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except Exception as e:
            print(f"TCP检查异常 {ip}:{port} - {str(e)}")
            return False
    
    def check_udp_port(self, ip, port):
        """
        使用SNMP v3检查UDP端口连通性
        执行命令：snmpget -v 3 -u test -l authPriv -a MD5 -A 'testauthPriv' -x AES -X 'testsuppPriv' ip:端口 *******.4.1.2011.**********.2.2.0

        Args:
            ip (str): IP地址
            port (int): 端口号

        Returns:
            tuple: (bool, str) - (是否连通, SNMP输出结果)
        """
        try:
            # 构建SNMP v3命令
            cmd = [
                'snmpget',
                '-v', '3',
                '-u', 'test',
                '-l', 'authPriv',
                '-a', 'MD5',
                '-A', 'testauthPriv',
                '-x', 'AES',
                '-X', 'testsuppPriv',
                f'{ip}:{port}',
                '*******.4.1.2011.**********.2.2.0'
            ]

            # 执行SNMP命令
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                timeout=self.timeout
            )

            # 获取完整的输出结果
            full_output = ""
            if result.stdout.strip():
                full_output += result.stdout.strip()
            if result.stderr.strip():
                if full_output:
                    full_output += " | "
                full_output += result.stderr.strip()

            # 判断连通性：只有包含"Timeout"的才是不通，其他都是通的
            if "Timeout" in full_output or "timeout" in full_output.lower():
                return False, full_output
            else:
                # 除了Timeout之外的所有结果都认为是连通的
                return True, full_output

        except subprocess.TimeoutExpired:
            timeout_msg = f"SNMP测试超时({self.timeout}秒)"
            print(f"⏰ {timeout_msg} {ip}:{port}")
            return False, timeout_msg
        except FileNotFoundError:
            error_msg = "snmpget命令不存在，请安装SNMP工具包"
            print(f"⚠️ {error_msg} {ip}:{port}")
            return False, error_msg
        except Exception as e:
            error_msg = f"SNMP测试异常: {str(e)}"
            print(f"❌ {error_msg} {ip}:{port}")
            return False, error_msg
    
    def check_single_port(self, row_data):
        """
        检查单个IP端口的连通性，并获取该IP的路由信息

        Args:
            row_data (tuple): (index, ip, port, protocol)

        Returns:
            tuple: (index, status, test_time, route_info, snmp_output)
        """
        index, ip, port, protocol = row_data
        test_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 获取该IP的具体路由信息
        route_info = self.get_route_for_ip(ip)
        snmp_output = ""  # 初始化SNMP输出

        try:
            port = int(port)
            protocol = str(protocol).upper().strip()

            if protocol == 'TCP':
                is_connected = self.check_tcp_port(ip, port)
                snmp_output = ""  # TCP协议留空
            elif protocol == 'UDP':
                is_connected, snmp_output = self.check_udp_port(ip, port)
            else:
                print(f"未知协议: {protocol}, 默认使用TCP")
                is_connected = self.check_tcp_port(ip, port)
                snmp_output = ""  # 未知协议使用TCP时也留空

            status = "连通" if is_connected else "不连通"
            status_emoji = "✅" if status == "连通" else "❌"
            protocol_emoji = "🔗" if protocol == 'TCP' else "📡"

            # 对于UDP协议，在输出中显示SNMP结果的简要信息
            if protocol == 'UDP' and snmp_output:
                snmp_brief = snmp_output[:50] + "..." if len(snmp_output) > 50 else snmp_output
                print(f"{status_emoji} 检查完成: {protocol_emoji} {ip}:{port} ({protocol}) - {status} [路由: {route_info}] [SNMP: {snmp_brief}]")
            else:
                print(f"{status_emoji} 检查完成: {protocol_emoji} {ip}:{port} ({protocol}) - {status} [路由: {route_info}]")

        except Exception as e:
            status = f"检查失败: {str(e)}"
            snmp_output = f"异常: {str(e)}"
            print(f"检查异常: {ip}:{port} - {str(e)}")

        return index, status, test_time, route_info, snmp_output

def load_excel_file(file_path, filter_conditions=None):
    """
    加载Excel文件

    Args:
        file_path (str): Excel文件路径
        filter_conditions (dict): 筛选条件字典

    Returns:
        pandas.DataFrame: 数据框
    """
    try:
        df = pd.read_excel(file_path,engine="openpyxl")
        print(f"📂 成功加载文件: {file_path}")
        print(f"📊 原始数据形状: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")

        # 应用筛选条件
        df = apply_filter_conditions(df, filter_conditions, 'excel')
        if df is None:
            return None

        return df
    except Exception as e:
        print(f"❌ 加载文件失败: {str(e)}")
        return None

def load_text_file(file_path, filter_conditions=None):
    """
    加载文本文件，支持格式：IP地址 端口 协议（用空格分隔）

    Args:
        file_path (str): 文本文件路径
        filter_conditions (dict): 筛选条件字典（对文本文件，只支持协议筛选）

    Returns:
        pandas.DataFrame: 数据框
    """
    try:
        data = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line or line.startswith('#'):  # 跳过空行和注释行
                    continue

                # 按空格分割，支持多个空格
                parts = line.split()
                if len(parts) >= 3:
                    ip = parts[0]
                    port = parts[1]
                    protocol = parts[2].upper()

                    # 可选的第4列：网络类型或其他信息
                    extra_info = {}
                    if len(parts) >= 4:
                        extra_info['网络类型'] = parts[3]

                    # 验证IP格式（简单验证）
                    if '.' in ip and port.isdigit():
                        row_data = {
                            'IP': ip,
                            '端口': int(port),
                            '协议': protocol
                        }
                        row_data.update(extra_info)
                        data.append(row_data)
                    else:
                        print(f"⚠️ 第{line_num}行格式错误，跳过: {line}")
                else:
                    print(f"⚠️ 第{line_num}行格式错误，跳过: {line}")

        if not data:
            print("❌ 文件中没有有效的IP端口数据")
            return None

        df = pd.DataFrame(data)
        print(f"📂 成功加载文件: {file_path}")
        print(f"📊 原始数据形状: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")

        # 应用筛选条件
        df = apply_filter_conditions(df, filter_conditions, 'text')
        if df is None:
            return None

        return df
    except Exception as e:
        print(f"❌ 加载文件失败: {str(e)}")
        return None

def validate_data(df):
    """
    验证数据格式

    Args:
        df (pandas.DataFrame): 数据框

    Returns:
        bool: 验证是否通过
    """
    required_columns = ['IP', '端口', '协议']

    for col in required_columns:
        if col not in df.columns:
            print(f"❌ 缺少必要列: {col}")
            return False

    # 检查空值
    for col in required_columns:
        null_count = df[col].isnull().sum()
        if null_count > 0:
            print(f"⚠️ 列 '{col}' 有 {null_count} 个空值")

    # 检查数据类型
    if not pd.api.types.is_numeric_dtype(df['端口']):
        print("⚠️ 端口列包含非数字值")

    return True

def parse_arguments():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(
        description='🔍 IP端口连通性测试工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
📋 使用示例:
  # Excel文件模式（默认）
  python ip_port_checker.py                                        # 使用默认筛选条件（网络类型=5GC,DC），按属地分组输出
  python ip_port_checker.py --no-filter                            # 不使用筛选条件，测试全部数据
  python ip_port_checker.py --filter "网元类型=IBCF,DCGW"          # 自定义普通筛选条件
  python ip_port_checker.py --regex-filter "IP=^192\\.168\\..*"    # 使用正则表达式筛选IP
  python ip_port_checker.py --filter "协议=TCP" --regex-filter "网元类型=.*GW$"  # 组合普通筛选和正则筛选
  python ip_port_checker.py --limit 1000                           # 限制测试前1000条数据
  python ip_port_checker.py --timeout 5 --workers 100             # 设置超时和并发数
  python ip_port_checker.py --no-group-by-region                   # 不按属地分组，保存到单个文件
  python ip_port_checker.py --region HBE                           # 只测试湖北属地
  python ip_port_checker.py --list-only --limit 100               # 只显示前100条测试列表

  # 正则表达式筛选示例
  python ip_port_checker.py --regex-filter "IP=^10\\..*"           # 匹配10开头的IP
  python ip_port_checker.py --regex-filter "属地=^H.*"             # 匹配H开头的属地
  python ip_port_checker.py --regex-filter "IP=^192\\.168\\..*;网元类型=.*GW$"  # AND: 多个正则条件
  python ip_port_checker.py --regex-filter "网络类型=5GC||网络类型=DC"  # OR: 网络类型是5GC或DC
  python ip_port_checker.py --regex-filter "IP=^192\\.168\\..*||IP=^10\\..*"  # OR: 多个IP段

  # 文本文件模式
  python ip_port_checker.py -i ip_list.txt --input-type text       # 测试文本文件（格式：IP 端口 协议）
  python ip_port_checker.py -i ip_list.txt -t text --filter "协议=TCP"  # 只测试TCP协议
  python ip_port_checker.py -i ip_list.txt -t text --regex-filter "IP=^172\\..*"  # 文本文件正则筛选
  python ip_port_checker.py -i ip_list.txt -t text --no-filter     # 测试文本文件中的所有IP端口

  # 协议筛选模式
  python ip_port_checker.py --protocol-only tcp                    # 仅测试数据中协议为TCP的记录
  python ip_port_checker.py --protocol-only udp                    # 仅测试数据中协议为UDP的记录
  python ip_port_checker.py -p tcp --filter "网络类型=5GC"         # 仅测试TCP协议记录，并应用其他筛选条件
        """
    )

    parser.add_argument('--input-file', '-i',
                       default='全国ip端口.xlsx',
                       help='📂 输入文件路径 (默认: 全国ip端口.xlsx)')

    parser.add_argument('--input-type', '-t',
                       choices=['excel', 'text', 'auto'],
                       default='auto',
                       help='📄 输入文件类型: excel(Excel文件), text(文本文件), auto(自动检测) (默认: auto)')

    parser.add_argument('--timeout',
                       type=int, default=8,
                       help='⏱️ 连接超时时间（秒） (默认: 8)')

    parser.add_argument('--workers', '-w',
                       type=int, default=50,
                       help='🔄 最大并发线程数 (默认: 50)')

    parser.add_argument('--limit', '-l',
                       type=int,
                       help='📊 限制测试数据条数，不设置则测试全部数据')

    parser.add_argument('--filter', '-f',
                       help='🔍 普通筛选条件，支持AND/OR逻辑: "列名=值1,值2" (多值) | "列名1=值1;列名2=值2" (AND) | "列名1=值1||列名2=值2" (OR) (Excel默认: 网络类型=5GC,DC; 文本文件只支持协议筛选)')

    parser.add_argument('--regex-filter', '-rf',
                       help='🔍 正则表达式筛选条件，支持AND/OR逻辑: "列名=正则表达式" | "列名1=正则1;列名2=正则2" (AND) | "列名1=正则1||列名2=正则2" (OR) (示例: "IP=^192\\.168\\..*" 或 "网络类型=5GC|DC")')

    parser.add_argument('--no-filter',
                       action='store_true',
                       help='🚫 不使用任何筛选条件')

    parser.add_argument('--list-columns',
                       action='store_true',
                       help='📋 显示可用的列名并退出（仅Excel文件）')

    parser.add_argument('--no-group-by-region',
                       action='store_true',
                       help='🚫 不按属地分组输出，所有结果保存在一个文件中（仅Excel文件）')

    parser.add_argument('--region', '-r',
                       help='🗺️ 指定测试某个属地（如：HBE, YND, HNY等）（仅Excel文件）')

    parser.add_argument('--list-only',
                       action='store_true',
                       help='📋 只输出要测试的列表，不执行实际测试')

    parser.add_argument('--protocol-only', '-p',
                       choices=['tcp', 'udp', 'TCP', 'UDP'],
                       help='🔗 仅测试指定协议的端口 (tcp/udp)，筛选出数据中对应协议的记录')

    return parser.parse_args()

def detect_file_type(file_path):
    """
    自动检测文件类型

    Args:
        file_path (str): 文件路径

    Returns:
        str: 文件类型 ('excel' 或 'text')
    """
    file_ext = os.path.splitext(file_path)[1].lower()
    if file_ext in ['.xlsx', '.xls']:
        return 'excel'
    elif file_ext in ['.txt', '.text', '.dat', '.csv']:
        return 'text'
    else:
        # 默认尝试文本格式
        return 'text'

def load_file(file_path, input_type='auto', filter_conditions=None):
    """
    根据文件类型加载文件

    Args:
        file_path (str): 文件路径
        input_type (str): 输入类型 ('excel', 'text', 'auto')
        filter_conditions (dict): 筛选条件字典

    Returns:
        pandas.DataFrame: 数据框
    """
    if input_type == 'auto':
        input_type = detect_file_type(file_path)
        print(f"🔍 自动检测文件类型: {input_type}")

    if input_type == 'excel':
        return load_excel_file(file_path, filter_conditions)
    elif input_type == 'text':
        return load_text_file(file_path, filter_conditions)
    else:
        print(f"❌ 不支持的文件类型: {input_type}")
        return None

def parse_filter_conditions(filter_str):
    """
    解析普通筛选条件字符串，支持AND和OR逻辑

    Args:
        filter_str (str): 筛选条件字符串，支持格式:
                         - AND关系: "列名=值1,值2" 或 "列名1=值1;列名2=值2"
                         - OR关系: "列名1=值1||列名2=值2"
                         - 混合: "列名1=值1;列名2=值2||列名3=值3"

    Returns:
        dict: 筛选条件字典，格式:
              - AND关系: {列名: 值}
              - OR关系: {'_or_conditions': [条件组1, 条件组2]}
    """
    if not filter_str:
        return None

    # 首先按 || 分割OR条件组
    or_groups = filter_str.split('||')

    if len(or_groups) == 1:
        # 没有OR条件，按原来的方式处理AND条件
        return _parse_and_conditions(or_groups[0])
    else:
        # 有OR条件，需要特殊处理
        or_conditions = []
        for group in or_groups:
            group_conditions = _parse_and_conditions(group.strip())
            if group_conditions:
                or_conditions.append(group_conditions)

        if or_conditions:
            return {'_or_conditions': or_conditions}
        return None

def _parse_and_conditions(condition_str):
    """
    解析AND条件组

    Args:
        condition_str (str): 条件字符串

    Returns:
        dict: 条件字典
    """
    conditions = {}
    # 按分号分割不同的筛选条件
    for condition in condition_str.split(';'):
        if '=' in condition:
            column, values = condition.split('=', 1)
            column = column.strip()
            # 按逗号分割多个值
            value_list = [v.strip() for v in values.split(',')]
            if len(value_list) == 1:
                conditions[column] = value_list[0]
            else:
                conditions[column] = value_list

    return conditions

def parse_regex_filter_conditions(filter_str):
    """
    解析正则表达式筛选条件字符串，支持AND和OR逻辑

    Args:
        filter_str (str): 正则筛选条件字符串，支持格式:
                         - AND关系: "列名=正则表达式" 或 "列名1=正则1;列名2=正则2"
                         - OR关系: "列名1=正则1||列名2=正则2"
                         - 混合: "列名1=正则1;列名2=正则2||列名3=正则3"

    Returns:
        dict: 正则筛选条件字典，格式:
              - AND关系: {列名: {'type': 'regex', 'pattern': '正则表达式'}}
              - OR关系: {'_or_conditions': [条件组1, 条件组2]}
    """
    if not filter_str:
        return None

    # 首先按 || 分割OR条件组
    or_groups = filter_str.split('||')

    if len(or_groups) == 1:
        # 没有OR条件，按原来的方式处理AND条件
        return _parse_regex_and_conditions(or_groups[0])
    else:
        # 有OR条件，需要特殊处理
        or_conditions = []
        for group in or_groups:
            group_conditions = _parse_regex_and_conditions(group.strip())
            if group_conditions:
                or_conditions.append(group_conditions)

        if or_conditions:
            return {'_or_conditions': or_conditions}
        return None

def _parse_regex_and_conditions(condition_str):
    """
    解析正则表达式AND条件组

    Args:
        condition_str (str): 条件字符串

    Returns:
        dict: 条件字典
    """
    conditions = {}
    # 按分号分割不同的筛选条件
    for condition in condition_str.split(';'):
        if '=' in condition:
            column, pattern = condition.split('=', 1)
            column = column.strip()
            pattern = pattern.strip()

            if not pattern:
                print(f"⚠️ 警告: 列 '{column}' 的正则表达式为空，跳过此筛选条件")
                continue

            # 验证正则表达式语法
            try:
                re.compile(pattern)
                conditions[column] = {
                    'type': 'regex',
                    'pattern': pattern
                }
                print(f"🔍 解析正则表达式筛选: {column} ~ /{pattern}/")
            except re.error as e:
                print(f"❌ 错误: 列 '{column}' 的正则表达式语法错误: {str(e)}")
                print(f"   正则表达式: {pattern}")
                continue

    return conditions

def apply_filter_conditions(df, filter_conditions, file_type='excel'):
    """
    应用筛选条件到数据框，支持普通匹配、正则表达式匹配和OR逻辑

    Args:
        df (pandas.DataFrame): 要筛选的数据框
        filter_conditions (dict): 筛选条件字典
        file_type (str): 文件类型，用于确定支持的筛选列

    Returns:
        pandas.DataFrame: 筛选后的数据框，如果筛选后无数据则返回None
    """
    if not filter_conditions:
        return df

    original_count = len(df)
    print(f"\n🔍 应用筛选条件:")

    # 检查是否有OR条件
    if '_or_conditions' in filter_conditions:
        return _apply_or_conditions(df, filter_conditions['_or_conditions'], file_type, original_count)

    # 处理普通AND条件
    return _apply_and_conditions(df, filter_conditions, file_type, original_count)

def _apply_or_conditions(df, or_conditions, file_type, original_count):
    """
    应用OR逻辑筛选条件
    """
    print(f"  🔀 使用OR逻辑，共 {len(or_conditions)} 个条件组:")

    # 收集所有满足任一条件组的行
    all_masks = []

    for i, condition_group in enumerate(or_conditions, 1):
        print(f"    📋 条件组 {i}:")
        group_df = _apply_and_conditions(df.copy(), condition_group, file_type, len(df), indent="      ")
        if group_df is not None:
            # 创建mask标记满足此条件组的行
            mask = df.index.isin(group_df.index)
            all_masks.append(mask)
            matched_count = mask.sum()
            print(f"      ✅ 条件组 {i} 匹配 {matched_count} 条记录")
        else:
            print(f"      ❌ 条件组 {i} 无匹配记录")

    if not all_masks:
        print("  ❌ 所有OR条件组都无匹配，筛选后没有数据")
        return None

    # 合并所有mask（OR逻辑）
    final_mask = all_masks[0]
    for mask in all_masks[1:]:
        final_mask = final_mask | mask

    result_df = df[final_mask]
    filtered_count = len(result_df)
    print(f"  📊 OR逻辑筛选结果: {original_count} → {filtered_count} 条记录")

    if filtered_count == 0:
        print("  ❌ 筛选后没有数据，请检查筛选条件")
        return None

    return result_df

def _apply_and_conditions(df, filter_conditions, file_type, original_count, indent="  "):
    """
    应用AND逻辑筛选条件
    """
    for column, condition in filter_conditions.items():
        if column not in df.columns:
            print(f"{indent}⚠️ 警告: 列 '{column}' 不存在，跳过此筛选条件")
            continue

        # 检查文本文件模式的列限制（支持常用列）
        if file_type == 'text' and column not in ['协议', 'IP', '端口', '网络类型']:
            print(f"{indent}⚠️ 警告: 文本文件模式只支持协议、IP、端口、网络类型筛选，跳过筛选条件 '{column}'")
            continue

        # 处理正则表达式匹配
        if isinstance(condition, dict) and condition.get('type') == 'regex':
            pattern = condition['pattern']
            try:
                # 使用正则表达式筛选，转换为字符串后匹配
                mask = df[column].astype(str).str.contains(pattern, regex=True, na=False)
                matched_count = mask.sum()
                df = df[mask]
                print(f"{indent}🔍 {column} 正则匹配 /{pattern}/: 匹配 {matched_count} 条记录")
            except Exception as e:
                print(f"{indent}❌ 正则表达式匹配失败 '{column}' ~ /{pattern}/: {str(e)}")
                continue

        # 处理普通匹配
        else:
            if isinstance(condition, list):
                # 多值匹配
                df = df[df[column].isin(condition)]
                print(f"{indent}📋 {column} 包含: {', '.join(map(str, condition))}")
            else:
                # 单值匹配
                df = df[df[column] == condition]
                print(f"{indent}📋 {column} 等于: {condition}")

    if indent == "  ":  # 只在顶层显示最终结果
        filtered_count = len(df)
        print(f"  📊 筛选结果: {original_count} → {filtered_count} 条记录")

        if filtered_count == 0:
            print("  ❌ 筛选后没有数据，请检查筛选条件")
            return None

    return df

def format_duration(seconds):
    """
    格式化时间显示，自动转换为合适的单位

    Args:
        seconds (float): 秒数

    Returns:
        str: 格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"

def signal_handler(signum, frame):
    """
    信号处理函数，用于优雅地处理Ctrl+C中断
    """
    global interrupted
    if not interrupted:  # 避免重复处理
        interrupted = True
        print(f"\n\n🛑 收到中断信号 (Ctrl+C)，正在停止测试...")
        print("⏳ 请稍等，正在取消剩余任务并保存已完成的结果...")
        print("💡 提示：程序将在当前任务完成后优雅退出")

def main():
    """
    主函数
    """
    # 设置信号处理器 (Linux环境优化)
    signal.signal(signal.SIGINT, signal_handler)
    # 在Linux下也可以处理SIGTERM信号
    signal.signal(signal.SIGTERM, signal_handler)

    # 解析命令行参数
    args = parse_arguments()

    # 配置参数
    input_file = args.input_file
    input_type = args.input_type
    timeout = args.timeout
    max_workers = args.workers
    test_limit = args.limit
    protocol_only = args.protocol_only.upper() if args.protocol_only else None

    # 创建输出目录
    output_dir = "check-result"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")

    # 生成带时间戳的输出文件名
    current_time = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 检测实际文件类型
    actual_file_type = detect_file_type(input_file) if input_type == 'auto' else input_type
    group_by_region = not args.no_group_by_region and actual_file_type == 'excel'

    # 处理筛选条件
    filter_conditions = None
    regex_filter_conditions = None

    if args.no_filter:
        print("🚫 不使用任何筛选条件")
    else:
        # 处理普通筛选条件
        if args.filter:
            filter_conditions = parse_filter_conditions(args.filter)
            print(f"🔍 使用普通筛选条件: {args.filter}")
        elif not args.regex_filter and not protocol_only:
            # 只有在没有指定任何筛选条件时才使用默认条件
            if actual_file_type == 'excel':
                # Excel文件默认筛选条件：网络类型是5GC和DC
                filter_conditions = {'网络类型': ['5GC', 'DC']}
                print("🎯 使用默认筛选条件: 网络类型=5GC,DC")
            else:
                # 文本文件默认不使用筛选条件
                print("📄 文本文件模式，默认不使用筛选条件")

        # 处理正则表达式筛选条件
        if args.regex_filter:
            regex_filter_conditions = parse_regex_filter_conditions(args.regex_filter)
            print(f"🔍 使用正则表达式筛选条件: {args.regex_filter}")

    # 合并筛选条件
    combined_filter_conditions = {}
    if filter_conditions:
        combined_filter_conditions.update(filter_conditions)
    if regex_filter_conditions:
        combined_filter_conditions.update(regex_filter_conditions)

    # 如果没有任何筛选条件，设置为None
    if not combined_filter_conditions:
        combined_filter_conditions = None

    # 处理协议筛选
    if protocol_only:
        if combined_filter_conditions is None:
            combined_filter_conditions = {}
        combined_filter_conditions['协议'] = protocol_only.upper()
        print(f"🔗 指定测试协议: {protocol_only.upper()}")

    # 处理属地筛选（仅Excel文件支持）
    if args.region:
        if actual_file_type == 'excel':
            if combined_filter_conditions is None:
                combined_filter_conditions = {}
            combined_filter_conditions['属地'] = args.region
            print(f"🗺️ 指定测试属地: {args.region}")
        else:
            print(f"⚠️ 警告: 文本文件模式不支持属地筛选，忽略 --region 参数")

    # 协议筛选说明
    if protocol_only:
        print(f"\n🎯 协议筛选模式: 仅测试 {protocol_only} 协议的记录")
        print(f"  📋 将筛选出数据中协议为 {protocol_only} 的记录进行测试")

    # UDP测试说明
    print("\n📡 UDP测试说明:")
    print("  🔧 本脚本使用SNMP v3进行UDP端口连通性测试")
    print("  📋 测试命令: snmpget -v 3 -u test -l authPriv -a MD5 -A 'testauthPriv' -x AES -X 'testsuppPriv' ip:端口 *******.4.1.2011.**********.2.2.0")
    print("  ⚠️  需要确保系统已安装snmpget工具（通常在net-snmp或snmp-utils包中）")
    print("  🎯 判断逻辑: 只有输出包含'Timeout'的才认为不连通，其他所有结果都认为连通")
    print("  📄 输出文件将包含完整的SNMP测试结果，便于分析")

    print("🌐" + "=" * 58 + "🌐")
    print("🔍 IP端口连通性测试工具 🔍")
    print("🌐" + "=" * 58 + "🌐")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 错误: 输入文件 '{input_file}' 不存在")
        return

    # 如果只是要查看列名，则加载文件并显示列名后退出（仅Excel文件支持）
    if args.list_columns:
        if actual_file_type == 'excel':
            try:
                df = pd.read_excel(input_file,engine="openpyxl")
                print("📋 可用的列名:")
                for i, col in enumerate(df.columns, 1):
                    print(f"  {i:2d}. {col}")
                print(f"\n📊 数据形状: {df.shape}")
                return
            except Exception as e:
                print(f"❌ 加载文件失败: {str(e)}")
                return
        else:
            print("⚠️ --list-columns 参数仅支持Excel文件")
            return

    # 加载文件
    df = load_file(input_file, input_type, combined_filter_conditions)
    if df is None:
        return
    
    # 验证数据格式
    if not validate_data(df):
        return
    
    # 显示前5行数据
    print("\n📋 前5行数据:")
    print(df.head())

    # 演示模式：只测试前N行数据
    if test_limit and test_limit < len(df):
        df = df.head(test_limit)
        print(f"\n🎯 演示模式：只测试前 {test_limit} 行数据")

    # 如果只是显示列表，则输出测试列表后退出
    if args.list_only:
        # 确定要显示的列
        display_columns = ['IP', '端口', '协议']

        # 根据筛选条件添加相关列
        if combined_filter_conditions:
            for column in combined_filter_conditions.keys():
                if column in df.columns and column not in display_columns:
                    display_columns.append(column)

        # 如果指定了属地且属地列不在显示列中，添加属地列
        if args.region and '属地' in df.columns and '属地' not in display_columns:
            display_columns.append('属地')

        print(f"\n📋 要测试的IP端口列表:")

        # 动态计算列宽
        col_widths = {}
        col_widths['序号'] = 6
        col_widths['IP'] = 15
        col_widths['端口'] = 6
        col_widths['协议'] = 6

        for col in display_columns[3:]:  # 跳过IP、端口、协议
            if col in df.columns:
                max_len = max(len(str(col)), df[col].astype(str).str.len().max())
                col_widths[col] = min(max_len + 2, 20)  # 最大宽度20

        # 构建表头
        header_line = f"{'序号':<{col_widths['序号']}} {'IP地址':<{col_widths['IP']}} {'端口':<{col_widths['端口']}} {'协议':<{col_widths['协议']}}"
        separator_line = "-" * (col_widths['序号'] + col_widths['IP'] + col_widths['端口'] + col_widths['协议'])

        for col in display_columns[3:]:
            if col in df.columns:
                header_line += f" {col:<{col_widths[col]}}"
                separator_line += "-" * (col_widths[col] + 1)

        print("=" * len(separator_line))
        print(header_line)
        print(separator_line)

        # 输出数据行
        for i, (index, row) in enumerate(df.iterrows(), 1):
            data_line = f"{i:<{col_widths['序号']}} {row['IP']:<{col_widths['IP']}} {row['端口']:<{col_widths['端口']}} {row['协议']:<{col_widths['协议']}}"

            for col in display_columns[3:]:
                if col in df.columns:
                    value = str(row[col])[:col_widths[col]-2]  # 截断过长的值
                    data_line += f" {value:<{col_widths[col]}}"

            print(data_line)

        print("=" * len(separator_line))
        print(f"📊 总计: {len(df)} 个IP端口待测试")

        # 按协议统计
        protocol_counts = df['协议'].value_counts()
        print(f"\n📈 协议统计:")
        for protocol, count in protocol_counts.items():
            emoji = "🔗" if protocol == 'TCP' else "📡"
            print(f"  {emoji} {protocol}: {count} 个")

        # 显示筛选条件相关的统计
        if combined_filter_conditions:
            for column in combined_filter_conditions.keys():
                if column in df.columns and column != '协议':
                    counts = df[column].value_counts()
                    print(f"\n📊 {column}统计:")
                    for value, count in counts.items():
                        print(f"  📍 {value}: {count} 个")

        print(f"\n💡 要执行实际测试，请去掉 --list-only 参数")
        return

    # 初始化端口检查器
    checker = PortChecker(timeout=timeout, max_workers=max_workers)
    print(f"🌐 检测到的默认网关: {checker.default_gateway}")

    # 准备测试数据
    test_data = []
    for index, row in df.iterrows():
        test_data.append((index, row['IP'], row['端口'], row['协议']))

    print(f"\n🚀 开始测试 {len(test_data)} 个IP端口...")
    print(f"⏱️  超时时间: {timeout}秒, 🔄 并发数: {max_workers}")
    
    # 初始化结果列
    df['状态'] = ""
    df['测试开始时间'] = ""
    df['路由信息'] = ""
    df['SNMP输出结果'] = None  # 使用None而不是空字符串，避免pandas转换为NaN
    
    # 使用线程池进行并发测试
    start_time = time.time()
    completed_count = 0

    try:
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_data = {executor.submit(checker.check_single_port, data): data for data in test_data}

            # 处理完成的任务
            try:
                for future in as_completed(future_to_data):
                    # 检查是否收到中断信号
                    if interrupted:
                        print(f"\n🛑 检测到中断信号，正在取消剩余任务...")
                        print(f"📊 已完成 {completed_count}/{len(test_data)} 个测试")
                        # 取消所有未完成的任务
                        for f in future_to_data:
                            if not f.done():
                                f.cancel()
                        print("✅ 任务取消完成")
                        break

                    try:
                        index, status, test_time, route_info, snmp_output = future.result()
                        df.loc[index, '状态'] = status
                        df.loc[index, '测试开始时间'] = test_time
                        df.loc[index, '路由信息'] = route_info
                        df.loc[index, 'SNMP输出结果'] = snmp_output

                        completed_count += 1
                        if completed_count % 100 == 0:
                            elapsed_time = time.time() - start_time
                            progress = completed_count/len(test_data)*100
                            print(f"📈 已完成: {completed_count}/{len(test_data)} ({progress:.1f}%), "
                                  f"⏱️ 耗时: {format_duration(elapsed_time)}")

                    except Exception as e:
                        print(f"处理任务时出错: {str(e)}")
            except KeyboardInterrupt:
                print(f"\n\n🛑 测试被用户中断！")
                print(f"📊 已完成 {completed_count}/{len(test_data)} 个测试")
                print("⏳ 正在取消剩余任务...")
                # 取消所有未完成的任务
                for future in future_to_data:
                    future.cancel()
                print("✅ 任务取消完成")
    except KeyboardInterrupt:
        print(f"\n\n🛑 测试被用户中断！")
        print(f"📊 已完成 {completed_count}/{len(test_data)} 个测试")
        print("⏳ 正在保存已完成的结果...")
    
    # 计算总耗时
    total_time = time.time() - start_time
    print(f"\n🎉 测试完成! 总耗时: {format_duration(total_time)}")

    # 统计结果
    status_counts = df['状态'].value_counts()
    print("\n📊 测试结果统计:")
    for status, count in status_counts.items():
        emoji = "❌" if "不连通" in status else "✅" if "连通" in status else "⚠️"
        print(f"  {emoji} {status}: {count} 个")
    
    # 保存结果前，将SNMP输出结果列的None值替换为空字符串
    df['SNMP输出结果'] = df['SNMP输出结果'].fillna('')

    # 保存结果
    try:
        if group_by_region and '属地' in df.columns:
            # 按属地分组保存
            print(f"\n📁 按属地分组保存结果...")
            regions = df['属地'].unique()
            saved_files = []

            # 创建按属地分组的统一文件夹
            group_dir = os.path.join(output_dir, f"group_by_region_{current_time}")
            if not os.path.exists(group_dir):
                os.makedirs(group_dir)
                print(f"  📁 创建分组目录: group_by_region_{current_time}")

            for region in regions:
                region_df = df[df['属地'] == region]
                region_file = os.path.join(group_dir, f"全国ip端口_测试结果_{region}_{current_time}.xlsx")
                region_df.to_excel(region_file, index=False)
                saved_files.append(region_file)
                print(f"  📍 {region}: {len(region_df)} 条记录 → 全国ip端口_测试结果_{region}_{current_time}.xlsx")

            print(f"\n💾 共保存 {len(saved_files)} 个属地文件到: {group_dir}")
            for file in saved_files:
                print(f"  📄 {os.path.basename(file)}")
        else:
            # 保存到单个文件
            output_file = os.path.join(output_dir, f"全国ip端口_测试结果_{current_time}.xlsx")
            df.to_excel(output_file, index=False)
            print(f"\n💾 结果已保存到: {output_file}")

    except Exception as e:
        print(f"❌ 保存文件失败: {str(e)}")

if __name__ == "__main__":
    main()
