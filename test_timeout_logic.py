#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Timeout判断逻辑
"""

import sys
import os
import subprocess
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 临时修改check_udp_port方法来使用模拟的snmpget
def mock_check_udp_port(ip, port, timeout=3):
    """
    使用模拟的snmpget测试UDP端口连通性
    """
    try:
        # 使用Python脚本模拟snmpget命令
        cmd = [
            sys.executable, 'mock_snmpget.py',
            '-v', '3',
            '-u', 'test',
            '-l', 'authPriv',
            '-a', 'MD5',
            '-A', 'testauthPriv',
            '-x', 'AES',
            '-X', 'testsuppPriv',
            f'{ip}:{port}',
            '*******.4.1.2011.**********.2.2.0'
        ]
        
        # 执行模拟SNMP命令
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            timeout=timeout
        )
        
        # 获取完整的输出结果
        full_output = ""
        if result.stdout.strip():
            full_output += result.stdout.strip()
        if result.stderr.strip():
            if full_output:
                full_output += " | "
            full_output += result.stderr.strip()
        
        # 判断连通性：只有包含"Timeout"的才是不通，其他都是通的
        if "Timeout" in full_output or "timeout" in full_output.lower():
            return False, full_output
        else:
            # 除了Timeout之外的所有结果都认为是连通的
            return True, full_output
            
    except subprocess.TimeoutExpired:
        timeout_msg = f"SNMP测试超时({timeout}秒)"
        return False, timeout_msg
    except FileNotFoundError:
        error_msg = "snmpget命令不存在，请安装SNMP工具包"
        return False, error_msg
    except Exception as e:
        error_msg = f"SNMP测试异常: {str(e)}"
        return False, error_msg

def test_timeout_logic():
    """
    测试不同情况下的连通性判断逻辑
    """
    print("🧪 测试Timeout判断逻辑")
    print("=" * 50)
    
    test_cases = [
        ("127.0.0.1", 161, "应该连通 - 正常SNMP响应"),
        ("*******", 161, "应该不连通 - Timeout"),
        ("***********", 161, "应该连通 - 认证失败但不是Timeout"),
        ("********", 161, "应该连通 - 主机不存在但不是Timeout"),
        ("*************", 161, "应该连通 - 默认成功响应"),
    ]
    
    print("📋 测试用例:")
    for i, (ip, port, description) in enumerate(test_cases, 1):
        print(f"  {i}. {ip}:{port} - {description}")
    
    print("\n🚀 开始测试...")
    print("-" * 50)
    
    for i, (ip, port, description) in enumerate(test_cases, 1):
        print(f"\n🔍 测试 {i}: {ip}:{port}")
        print(f"   预期: {description}")
        
        try:
            is_connected, snmp_output = mock_check_udp_port(ip, port)
            status = "✅ 连通" if is_connected else "❌ 不连通"
            print(f"   结果: {status}")
            print(f"   SNMP输出: {snmp_output}")
            
            # 验证逻辑是否正确
            if "Timeout" in snmp_output or "timeout" in snmp_output.lower():
                expected_connected = False
            else:
                expected_connected = True
                
            if is_connected == expected_connected:
                print(f"   ✅ 判断逻辑正确")
            else:
                print(f"   ❌ 判断逻辑错误！预期: {'连通' if expected_connected else '不连通'}")
                
        except Exception as e:
            print(f"   ❌ 测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成!")
    print("\n💡 判断逻辑说明:")
    print("  - 只有输出包含'Timeout'的才认为不连通")
    print("  - 认证失败、主机不存在等其他错误都认为连通")
    print("  - 这样可以区分真正的网络不可达和SNMP配置问题")

if __name__ == "__main__":
    test_timeout_logic()
