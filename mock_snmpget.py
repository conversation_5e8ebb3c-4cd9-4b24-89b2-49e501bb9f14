#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模拟snmpget命令的测试脚本
用于测试不同的SNMP输出结果和连通性判断逻辑
"""

import sys
import os

def mock_snmpget():
    """
    模拟snmpget命令的不同输出结果
    """
    if len(sys.argv) < 2:
        print("Usage: python mock_snmpget.py <target>")
        sys.exit(1)
    
    target = sys.argv[-2]  # 倒数第二个参数是目标IP:端口
    
    # 根据不同的IP模拟不同的输出结果
    if "127.0.0.1" in target:
        # 模拟成功的SNMP响应
        print("SNMPv2-SMI::enterprises.2011.**********.2.2.0 = INTEGER: 1")
        sys.exit(0)
    elif "*******" in target:
        # 模拟超时
        print("Timeout: No Response from *******", file=sys.stderr)
        sys.exit(1)
    elif "***********" in target:
        # 模拟认证失败但不是超时
        print("snmpget: Authentication failure (incorrect password, community or key)", file=sys.stderr)
        sys.exit(1)
    elif "********" in target:
        # 模拟其他错误
        print("snmpget: Unknown host (********)", file=sys.stderr)
        sys.exit(1)
    else:
        # 默认成功响应
        print("SNMPv2-SMI::enterprises.2011.**********.2.2.0 = STRING: \"Test Response\"")
        sys.exit(0)

if __name__ == "__main__":
    mock_snmpget()
